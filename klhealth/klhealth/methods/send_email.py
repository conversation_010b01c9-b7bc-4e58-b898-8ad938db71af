import frappe
from frappe import _
import re
import csv
import io
from datetime import datetime

@frappe.whitelist()
def send_collective_invoice_email_with_csv(collective_invoice, recipient_email, email_subject, email_message, csv_data=None, csv_filename=None):
    """
    Send collective invoice email with PDF and CSV attachments
    Updated to accept CSV data from JavaScript frontend
    """
    try:
        # Validate email format
        email_pattern = r'^[^\s@]+@[^\s@]+\.[^\s@]+$'
        if not re.match(email_pattern, recipient_email):
            return {
                "success": False,
                "error": "Invalid email address format"
            }
        
        # Get the collective invoice document
        doc = frappe.get_doc("Collective Invoices", collective_invoice)
        
        # Get the default print format for Collective Invoices
        print_format = frappe.db.get_value("Property Setter", 
                                          {"doc_type": "Collective Invoices", "property": "default_print_format"}, 
                                          "value") or "Standard"
        
        # Generate PDF attachment
        pdf_data = frappe.get_print(
            "Collective Invoices", 
            doc.name, 
            print_format=print_format,
            as_pdf=True
        )
        
        # Prepare attachments - always include PDF
        attachments = [
            {
                "fname": f"Collective_Invoice_{doc.name}.pdf",
                "fcontent": pdf_data
            }
        ]
        
        # Add CSV attachment if data provided from frontend
        csv_attached = False
        if csv_data and csv_filename:
            attachments.append({
                "fname": csv_filename,
                "fcontent": csv_data.encode('utf-8')
            })
            csv_attached = True
            frappe.log_error(f"CSV attachment added for {doc.name} - {len(csv_data)} characters", "CSV Debug")
        else:
            # Fallback: try to generate CSV using backend method
            backend_csv_data = generate_timecard_csv(doc)
            if backend_csv_data:
                attachments.append({
                    "fname": f"Timecard_Details_{doc.name}.csv",
                    "fcontent": backend_csv_data.encode('utf-8')
                })
                csv_attached = True
                frappe.log_error(f"Backend CSV attachment added for {doc.name} - {len(backend_csv_data)} characters", "CSV Debug")
            else:
                frappe.log_error(f"No CSV data generated for {doc.name}", "CSV Debug")
        
        # Send email with attachments
        frappe.sendmail(
            recipients=[recipient_email],
            subject=email_subject,
            message=email_message,
            attachments=attachments,
            reference_doctype="Collective Invoices",
            reference_name=doc.name
        )
        
        # Log the email sending activity
        frappe.get_doc({
            "doctype": "Communication",
            "communication_type": "Communication",
            "communication_medium": "Email",
            "sent_or_received": "Sent",
            "email_status": "Open",
            "subject": email_subject,
            "content": email_message,
            "status": "Linked",
            "reference_doctype": "Collective Invoices",
            "reference_name": doc.name,
            "recipients": recipient_email,
            "sender": frappe.session.user
        }).insert(ignore_permissions=True)
        
        return {
            "success": True,
            "message": f"Email sent successfully to {recipient_email}",
            "csv_attached": csv_attached
        }
        
    except frappe.DoesNotExistError:
        return {
            "success": False,
            "error": f"Document not found"
        }
        
    except Exception as e:
        frappe.log_error(f"Error sending collective invoice email with CSV: {str(e)}")
        return {
            "success": False,
            "error": f"Failed to send email: {str(e)}"
        }


@frappe.whitelist()
def generate_timecard_csv(collective_invoice_doc):
    """
    Generate CSV data for Timecard Invoice Detail based on customer location and date range
    Enhanced with proper date and location filtering
    """
    try:
        # Get customer from collective invoice
        customer = collective_invoice_doc.customer
        if not customer:
            frappe.log_error("No customer found in collective invoice", "CSV Generation")
            return None
        
        # Get location from KL Customers doctype using company_name field
        location = frappe.db.get_value("KL Customers", {"company_name": customer}, "location")
        if not location:
            frappe.log_error(f"No location found for customer: {customer} in KL Customers", "CSV Generation")
            return None
        
        # Parse date range from collective invoice (format: "06/29/25 to 07/12/25")
        date_range = collective_invoice_doc.date_range
        if not date_range:
            frappe.log_error("No date range found in collective invoice", "CSV Generation")
            return None
        
        start_date, end_date = parse_date_range(date_range)
        if not start_date or not end_date:
            frappe.log_error(f"Could not parse date range: {date_range}", "CSV Generation")
            return None
        
        frappe.log_error(f"Searching for records - Customer: {customer}, Location: {location}, Start: {start_date}, End: {end_date}", "CSV Generation")
        
        # Get all timecard details for the location first
        timecard_details = frappe.get_all(
            "Timecard Invoice Detail",
            filters={
                "location": location
            },
            fields=[
                "name", "adp_employee_name", "employee_type", "location", "day", 
                "date", "start_time", "end_time", "regular_dec", "overtime_dec", 
                "total_hours_worked", "sd_category", "sd_range", "sd_time",
                "regular_rate", "overtime_rate", "holiday_rate", "sd_rate",
                "regular_invoice_amount", "overtime_invoice_amount", 
                "holiday_invoice_amount", "sd_invoice_amount", "invoice_status",
                "total_invoice_amount", "comment"
            ]
        )
        
        # Filter by date range manually to handle different date formats
        filtered_details = []
        for record in timecard_details:
            if record.get('date'):
                try:
                    # Convert the record date to comparable format
                    record_date = parse_single_date_for_comparison(str(record['date']))
                    
                    if record_date and start_date <= record_date <= end_date:
                        filtered_details.append(record)
                        frappe.log_error(f"Including record: {record['name']} with date {record['date']} (parsed as {record_date})", "CSV Generation")
                    else:
                        frappe.log_error(f"Excluding record: {record['name']} with date {record['date']} (parsed as {record_date}) - outside range {start_date} to {end_date}", "CSV Generation")
                        
                except Exception as e:
                    frappe.log_error(f"Could not parse date: {record['date']} for record {record.get('name')} - {str(e)}", "CSV Generation")
                    continue
        
        if not filtered_details:
            frappe.log_error(f"No timecard details found for location: {location}, date range: {start_date} to {end_date}", "CSV Generation")
            return None
        
        frappe.log_error(f"Found {len(filtered_details)} timecard records for CSV generation", "CSV Generation")
        
        # Define CSV headers based on the DocType fields
        csv_headers = [
            "Employee Name", "Employee Type", "Location", "Day", "Date", 
            "Start Time", "End Time", "Regular Hours", "Overtime Hours", 
            "Total Hours Worked", "SD Category", "SD Range", "SD Time",
            "Regular Rate", "Overtime Rate", "Holiday Rate", "SD Rate",
            "Regular Amount", "Overtime Amount", "Holiday Amount", "SD Amount",
            "Status", "Total Amount", "Comment"
        ]
        
        # Generate CSV content
        output = io.StringIO()
        writer = csv.writer(output)
        
        # Write header
        writer.writerow(csv_headers)
        
        # Write data rows
        for record in filtered_details:
            row = [
                record.get('adp_employee_name', ''),
                record.get('employee_type', ''),
                record.get('location', ''),
                record.get('day', ''),
                record.get('date', ''),
                record.get('start_time', ''),
                record.get('end_time', ''),
                record.get('regular_dec', ''),
                record.get('overtime_dec', ''),
                record.get('total_hours_worked', ''),
                record.get('sd_category', ''),
                record.get('sd_range', ''),
                record.get('sd_time', ''),
                record.get('regular_rate', ''),
                record.get('overtime_rate', ''),
                record.get('holiday_rate', ''),
                record.get('sd_rate', ''),
                record.get('regular_invoice_amount', ''),
                record.get('overtime_invoice_amount', ''),
                record.get('holiday_invoice_amount', ''),
                record.get('sd_invoice_amount', ''),
                record.get('invoice_status', ''),
                record.get('total_invoice_amount', ''),
                record.get('comment', '')
            ]
            writer.writerow(row)
        
        csv_content = output.getvalue()
        output.close()
        
        frappe.log_error(f"Generated CSV with {len(csv_content)} characters for {len(filtered_details)} records", "CSV Generation")
        return csv_content
        
    except Exception as e:
        frappe.log_error(f"Error generating timecard CSV: {str(e)}", "CSV Generation")
        return None


def parse_date_range(date_range_str):
    """
    Parse date range string like "06/29/25 to 07/12/25" into start and end dates
    Returns date objects for comparison
    """
    try:
        # Split the date range
        parts = date_range_str.split(" to ")
        if len(parts) != 2:
            frappe.log_error(f"Invalid date range format: {date_range_str} - should be 'MM/DD/YY to MM/DD/YY'", "Date Parsing")
            return None, None
        
        start_str = parts[0].strip()
        end_str = parts[1].strip()
        
        # Parse dates
        start_date = parse_single_date_for_comparison(start_str)
        end_date = parse_single_date_for_comparison(end_str)
        
        if not start_date or not end_date:
            frappe.log_error(f"Could not parse one or both dates: start='{start_str}' -> {start_date}, end='{end_str}' -> {end_date}", "Date Parsing")
            return None, None
        
        frappe.log_error(f"Parsed date range: '{date_range_str}' -> {start_date} to {end_date}", "Date Parsing")
        return start_date, end_date
        
    except Exception as e:
        frappe.log_error(f"Error parsing date range {date_range_str}: {str(e)}", "Date Parsing")
        return None, None


def parse_single_date_for_comparison(date_str):
    """
    Parse a single date string and return a date object for comparison
    Handles multiple formats: MM/DD/YY, MM/DD/YYYY, YYYY-MM-DD
    """
    try:
        if not date_str:
            return None
            
        date_str = str(date_str).strip()
        
        # Handle MM/DD/YY or MM/DD/YYYY format
        if '/' in date_str:
            parts = date_str.split('/')
            if len(parts) == 3:
                month, day, year = parts
                
                # Convert 2-digit year to 4-digit year
                if len(year) == 2:
                    year_int = int(year)
                    current_year = datetime.now().year
                    
                    # Assume years 00-30 are 2000s, 31-99 are 1900s
                    if year_int <= 30:
                        year = str(2000 + year_int)
                    else:
                        year = str(1900 + year_int)
                elif len(year) == 4:
                    year = year
                else:
                    frappe.log_error(f"Invalid year format in date: {date_str}", "Date Parsing")
                    return None
                
                # Create date object
                try:
                    return datetime(int(year), int(month), int(day)).date()
                except ValueError as ve:
                    frappe.log_error(f"Invalid date values: year={year}, month={month}, day={day} from {date_str} - {str(ve)}", "Date Parsing")
                    return None
        
        # Handle YYYY-MM-DD format
        elif '-' in date_str:
            try:
                return datetime.strptime(date_str, "%Y-%m-%d").date()
            except ValueError:
                frappe.log_error(f"Could not parse YYYY-MM-DD format: {date_str}", "Date Parsing")
                return None
        
        # Handle other potential formats
        else:
            frappe.log_error(f"Unrecognized date format: {date_str}", "Date Parsing")
            return None
            
    except Exception as e:
        frappe.log_error(f"Error parsing single date {date_str}: {str(e)}", "Date Parsing")
        return None


# Keep your existing helper functions
def parse_single_date(date_str):
    """
    Parse a single date string with improved year handling
    (Keep this for backward compatibility)
    """
    return parse_single_date_for_comparison(date_str)


@frappe.whitelist()
def get_default_print_format(doctype):
    """
    Get the default print format for a doctype
    """
    try:
        # First check if there's a custom default print format set
        default_print_format = frappe.db.get_value("Property Setter", 
                                                  {"doc_type": doctype, "property": "default_print_format"}, 
                                                  "value")
        
        if default_print_format:
            return default_print_format
        
        # If no custom default, check if there's a print format specifically for this doctype
        print_formats = frappe.get_all(
            "Print Format",
            filters={"doc_type": doctype, "disabled": 0},
            fields=["name"],
            limit=1
        )
        
        if print_formats:
            return print_formats[0].name
            
        # Default to "Standard" if nothing else is found
        return "Standard"
        
    except Exception as e:
        frappe.log_error(f"Error getting default print format: {str(e)}")
        return "Standard"


@frappe.whitelist() 
def preview_email_template(collective_invoice):
    """
    Preview the hardcoded email template with actual document data
    This is optional - for testing/preview purposes
    """
    try:
        doc = frappe.get_doc("Collective Invoices", collective_invoice)
        
        # Format the email content with actual document data
        subject = f"Collective Invoice - {doc.name}"
        
        message = f"""Dear {doc.customer or 'Customer'},

Please find attached your collective invoice for the period: {doc.date_range or 'N/A'}.

Invoice Details:
- Invoice Number: {doc.name}
- Total Amount: {frappe.utils.fmt_money(doc.total_amount, currency=frappe.defaults.get_global_default("currency")) if doc.total_amount else 'N/A'}
- Due Date: {frappe.utils.formatdate(doc.due_date) if doc.due_date else 'N/A'}

Please process the payment at your earliest convenience.

Thank you for your business!

Best Regards,
{frappe.defaults.get_global_default("company") or 'Your Company'}"""
        
        return {
            "success": True,
            "subject": subject,
            "message": message
        }
        
    except Exception as e:
        frappe.log_error(f"Error previewing email template: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }


# Debug function to test CSV generation
@frappe.whitelist()
def test_csv_generation(collective_invoice):
    """
    Test function to debug CSV generation with enhanced logging
    """
    try:
        doc = frappe.get_doc("Collective Invoices", collective_invoice)
        csv_data = generate_timecard_csv(doc)
        
        # Additional debug info
        customer = doc.customer
        location = None
        if customer:
            location = frappe.db.get_value("KL Customers", {"company_name": customer}, "location")
        
        # Parse date range for debugging
        start_date, end_date = None, None
        if doc.date_range:
            start_date, end_date = parse_date_range(doc.date_range)
        
        # Get count of all records for location (without date filter)
        total_location_records = 0
        if location:
            total_location_records = frappe.db.count("Timecard Invoice Detail", {"location": location})
        
        return {
            "success": True,
            "csv_generated": bool(csv_data),
            "csv_length": len(csv_data) if csv_data else 0,
            "customer": customer,
            "location": location,
            "date_range": doc.date_range,
            "parsed_start_date": str(start_date) if start_date else None,
            "parsed_end_date": str(end_date) if end_date else None,
            "total_location_records": total_location_records,
            "csv_preview": csv_data[:500] if csv_data else None 
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }


@frappe.whitelist()
def debug_timecard_query(collective_invoice):
    """
    Debug function to test the timecard query filtering with enhanced information
    """
    try:
        doc = frappe.get_doc("Collective Invoices", collective_invoice)
        customer = doc.customer
        
        # Get location
        location = frappe.db.get_value("KL Customers", {"company_name": customer}, "location")
        
        # Parse dates
        start_date, end_date = parse_date_range(doc.date_range) if doc.date_range else (None, None)
        
        # Get all timecard details for this location (without date filter)
        all_records = frappe.get_all(
            "Timecard Invoice Detail",
            filters={"location": location},
            fields=["name", "date", "location", "adp_employee_name"],
            limit_page_length=20
        )
        
        # Test date parsing on sample records
        date_parsing_test = []
        for record in all_records[:5]:
            if record.get('date'):
                parsed_date = parse_single_date_for_comparison(str(record['date']))
                date_parsing_test.append({
                    "original_date": record['date'],
                    "parsed_date": str(parsed_date) if parsed_date else None,
                    "in_range": bool(parsed_date and start_date and end_date and start_date <= parsed_date <= end_date)
                })
        
        return {
            "success": True,
            "customer": customer,
            "location": location,
            "date_range": doc.date_range,
            "parsed_start_date": str(start_date) if start_date else None,
            "parsed_end_date": str(end_date) if end_date else None,
            "total_records_for_location": len(all_records),
            "sample_records": all_records[:5],
            "date_parsing_test": date_parsing_test
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }