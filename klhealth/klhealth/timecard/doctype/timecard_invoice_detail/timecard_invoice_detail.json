{"actions": [], "allow_rename": 1, "autoname": "format: {adp_employee_name}-{employee_type}-{date}-{start_time}-{end_time}", "creation": "2025-07-05 10:54:24.903718", "doctype": "DocType", "title_field": "adp_employee_name", "engine": "InnoDB", "field_order": ["invoice_details_section", "adp_employee_name", "employee_type", "location", "day", "date", "start_time", "end_time", "column_break_kopb", "regular_dec", "overtime_dec", "total_hours_worked", "sd_category", "sd_range", "sd_time", "rate_details_section", "regular_rate", "overtime_rate", "column_break_mvfj", "holiday_rate", "sd_rate", "amount_details_section", "regular_invoice_amount", "overtime_invoice_amount", "holiday_invoice_amount", "sd_invoice_amount", "invoice_status", "column_break_ygjq", "total_invoice_amount", "comment"], "fields": [{"fieldname": "invoice_details_section", "fieldtype": "Section Break", "label": "Invoice Details"}, {"fieldname": "employee_type", "fieldtype": "Data", "label": "Employee Type"}, {"fieldname": "location", "fieldtype": "Data", "label": "Location"}, {"fieldname": "day", "fieldtype": "Data", "label": "Day"}, {"fieldname": "date", "fieldtype": "Data", "label": "Date"}, {"fieldname": "start_time", "fieldtype": "Data", "label": "Start Time"}, {"fieldname": "end_time", "fieldtype": "Data", "label": "End Time"}, {"fieldname": "column_break_kopb", "fieldtype": "Column Break"}, {"fieldname": "regular_dec", "fieldtype": "Data", "label": "Regular_dec"}, {"fieldname": "overtime_dec", "fieldtype": "Data", "label": "Ovetime_dec"}, {"fieldname": "total_hours_worked", "fieldtype": "Float", "label": "Total Hours Worked"}, {"fieldname": "sd_category", "fieldtype": "Data", "label": "SD Category"}, {"fieldname": "sd_range", "fieldtype": "Data", "label": "SD Range"}, {"fieldname": "sd_time", "fieldtype": "Float", "label": "SD Time"}, {"fieldname": "rate_details_section", "fieldtype": "Section Break", "label": "Rate Details"}, {"fieldname": "regular_rate", "fieldtype": "Data", "label": "Regular Rate"}, {"fieldname": "overtime_rate", "fieldtype": "Data", "label": "Overtime Rate"}, {"fieldname": "column_break_mvfj", "fieldtype": "Column Break"}, {"fieldname": "holiday_rate", "fieldtype": "Data", "label": "Holiday Rate"}, {"fieldname": "sd_rate", "fieldtype": "Data", "label": "SD Rate"}, {"fieldname": "amount_details_section", "fieldtype": "Section Break", "label": "Amount Details"}, {"fieldname": "regular_invoice_amount", "fieldtype": "Data", "label": "Regular Invoice Amount"}, {"fieldname": "overtime_invoice_amount", "fieldtype": "Data", "label": "Overtime Invoice Amount"}, {"fieldname": "holiday_invoice_amount", "fieldtype": "Data", "label": "Holiday Invoice Amount"}, {"fieldname": "sd_invoice_amount", "fieldtype": "Data", "label": "SD Invoice Amount"}, {"fieldname": "invoice_status", "fieldtype": "Select", "label": "Invoice Status", "options": "\nReady\nApproved\nHold\nDen<PERSON>\nComplete"}, {"fieldname": "column_break_ygjq", "fieldtype": "Column Break"}, {"fieldname": "total_invoice_amount", "fieldtype": "Float", "label": "Total Invoice Amount", "precision": "2"}, {"fieldname": "comment", "fieldtype": "Small Text", "label": "Comment"}, {"fieldname": "adp_employee_name", "fieldtype": "Data", "label": "ADP_EmployeeName"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-07-17 14:26:37.720080", "modified_by": "Administrator", "module": "Timecard", "name": "Timecard Invoice Detail", "naming_rule": "Expression", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": []}