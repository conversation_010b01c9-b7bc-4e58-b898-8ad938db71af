{"actions": [], "allow_rename": 1, "autoname": "format:{customer}-{date_range}", "creation": "2025-07-15 10:08:39.227175", "doctype": "DocType", "engine": "InnoDB", "field_order": ["posting_date", "date_range", "due_date", "customer", "reference_invoices", "total_amount", "status", "amended_from"], "fields": [{"fieldname": "customer", "fieldtype": "Data", "label": "Customer"}, {"fieldname": "reference_invoices", "fieldtype": "Table", "label": "Reference Invoices", "options": "Reference Invoices"}, {"fieldname": "total_amount", "fieldtype": "Float", "label": "Total Amount"}, {"fieldname": "posting_date", "fieldtype": "Date", "label": "Posting Date"}, {"fieldname": "due_date", "fieldtype": "Date", "label": "Due Date"}, {"fieldname": "date_range", "fieldtype": "Data", "in_list_view": 1, "label": "Date Range"}, {"fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "label": "Status", "options": "Unpaid\nPaid\nPartly Paid\nOverdue"}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "Collective Invoices", "print_hide": 1, "read_only": 1, "search_index": 1}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-07-23 15:14:30.281338", "modified_by": "Administrator", "module": "Timecard", "name": "Collective Invoices", "naming_rule": "Expression", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": []}