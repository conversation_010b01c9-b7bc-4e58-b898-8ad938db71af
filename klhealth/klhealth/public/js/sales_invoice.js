frappe.listview_settings['Sales Invoice'] = {
    hide_name_column: true,
    onload: function (listview) {
             listview.setup_side_bar = function() {
        };
        listview.$page.find('.layout-side-section').remove();
        listview.page.add_inner_button("Generate Combined Invoice", function() {
            let dialog = new frappe.ui.Dialog({
                title: __('Generate Combined Invoice'),
                fields: [
                    {
                        fieldname: 'start_date',
                        fieldtype: 'Date',
                        label: __('Start Date'),
                        reqd: 1,
                        default: frappe.datetime.add_days(frappe.datetime.get_today(), -30)
                    },
                    {
                        fieldname: 'end_date',
                        fieldtype: 'Date',
                        label: __('End Date'),
                        reqd: 1,
                        default: frappe.datetime.get_today()
                    }
                ],
                primary_action_label: __('Generate'),
                primary_action: function(values) {
                    let start_date = values.start_date;
                    let end_date = values.end_date;
                    
                    if (!start_date || !end_date) {
                        frappe.msgprint(__('Please select both start and end dates.'));
                        return;
                    }
                    
                    if (start_date > end_date) {
                        frappe.msgprint(__('Start date cannot be greater than end date.'));
                        return;
                    }
                    
                    dialog.hide();
                    
                    frappe.show_alert({
                        message: __('Generating invoice details...'),
                        indicator: 'blue'
                    });
                    
                    // Convert dates to DD/MM/YYYY format if needed
                    function convertDateFormat(dateStr) {
                        if (!dateStr) return dateStr;
                        
                        // If it's already in YYYY-MM-DD format, convert to DD/MM/YYYY
                        if (dateStr.match(/^\d{4}-\d{2}-\d{2}$/)) {
                            let parts = dateStr.split('-');
                            return parts[2] + '/' + parts[1] + '/' + parts[0];
                        }
                        
                        return dateStr; // Return as-is if different format
                    }
                    
                    frappe.call({
                        method: 'klhealth.klhealth.generate_combined_invoice.generate_combined_invoice',
                        args: {
                            'start_date': start_date,  // Keep ISO format - let backend handle it
                            'end_date': end_date
                        },
                        freeze: true,
                        freeze_message: __('Processing timecards and generating invoice details...'),
                        callback: function(r) {
                            if (r.message && r.message.success) {
                                frappe.msgprint({
                                    title: __('Invoice Generation Complete'),
                                    message: r.message.message,
                                    indicator: 'green'
                                });
                                
                                setTimeout(function() {
                                    location.reload();
                                }, 2000);
                            } else {
                                frappe.msgprint({
                                    title: __('Error'),
                                    message: r.message ? r.message.message : __('Error occurred while generating invoice details'),
                                    indicator: 'red'
                                });
                            }
                        },
                        error: function(r) {
                            frappe.msgprint({
                                title: __('Error'),
                                message: __('Error occurred while generating invoice details'),
                                indicator: 'red'
                            });
                        }
                    });
                }
            });
            
            dialog.show();
        });
    }
};