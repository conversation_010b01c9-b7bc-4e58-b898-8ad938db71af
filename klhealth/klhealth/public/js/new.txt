frappe.views.Workspace = class Workspace extends frappe.views.Workspace {
	constructor(opts) {
		super(opts);
		frappe.workspace = this;

		this.sidebar_sections = this.getSidebarSections();
		this.workspace_save_in_progress = false;
		this.current_doctype_view = null;
		this.active_section = 'workspace';
		this.layout_initialized = false;
		this.content_loaded = false;
		this.initialization_attempts = 0;
		this.max_attempts = 10;
		this.editor_ready = false;
		this.content_persistence_interval = null;

		// Prevent default workspace behavior that might conflict
		this.disable_default_refresh = true;

		// Enhanced initialization with better error handling
		this.initializeWorkspace();

		// Enhanced workspace save handler
		frappe.realtime.on("workspace:save-success", () => {
			this.workspace_save_in_progress = true;
			console.log("Workspace save detected, auto-refreshing...");
			
			setTimeout(() => {
				window.location.reload();
			}, 500);
		});

		this.setupSaveButtonObserver();
	}

	async initializeWorkspace() {
		try {
			// Wait for frappe to be fully ready
			await this.waitForFrappeReady();
			
			// Initialize after DOM is ready
			if (document.readyState === 'loading') {
				document.addEventListener('DOMContentLoaded', () => {
					this.setupWithRetry();
				});
			} else {
				this.setupWithRetry();
			}
		} catch (error) {
			console.error('Workspace initialization error:', error);
			this.retryInitialization();
		}
	}

	waitForFrappeReady() {
		return new Promise((resolve) => {
			const checkReady = () => {
				if (frappe && frappe.ready && this.page && this.page.main) {
					resolve();
				} else {
					setTimeout(checkReady, 100);
				}
			};
			checkReady();
		});
	}

	setupWithRetry() {
		this.initialization_attempts++;
		
		if (this.initialization_attempts > this.max_attempts) {
			console.error('Max initialization attempts reached');
			return;
		}

		try {
			if (this.page && this.page.main && !this.layout_initialized) {
				console.log(`Initialization attempt ${this.initialization_attempts}`);
				this.setup_unified_layout();
			} else {
				setTimeout(() => {
					this.setupWithRetry();
				}, 200 * this.initialization_attempts);
			}
		} catch (error) {
			console.error('Setup error:', error);
			setTimeout(() => {
				this.setupWithRetry();
			}, 300 * this.initialization_attempts);
		}
	}

	retryInitialization() {
		setTimeout(() => {
			this.initializeWorkspace();
		}, 1000);
	}

	setupSaveButtonObserver() {
		let saveTimeout;
		
		$(document).on('click', '.btn-primary[data-label="Save"]', () => {
			clearTimeout(saveTimeout);
			console.log("Save button clicked, preparing for auto-refresh...");
			this.workspace_save_in_progress = true;
			
			saveTimeout = setTimeout(() => {
				if (this.workspace_save_in_progress) {
					console.log("Auto-refreshing page after save...");
					window.location.reload();
				}
			}, 1200);
		});

		$(document).on('keydown', (e) => {
			if ((e.ctrlKey || e.metaKey) && e.key === 's') {
				clearTimeout(saveTimeout);
				console.log("Keyboard save detected, preparing for auto-refresh...");
				this.workspace_save_in_progress = true;
				
				saveTimeout = setTimeout(() => {
					if (this.workspace_save_in_progress) {
						console.log("Auto-refreshing page after keyboard save...");
						window.location.reload();
					}
				}, 1200);
			}
		});
	}

	setup_unified_layout() {
		if (!this.page || !this.page.main || this.layout_initialized) {
			return;
		}

		try {
			console.log("Setting up unified layout...");
			
			// CRITICAL: Stop any existing refresh mechanisms
			this.stopDefaultRefresh();
			
			// Clear any existing content safely
			this.page.clear_inner_toolbar();
			const main_section = this.page.main.empty();

			// Create the unified layout structure
			const unifiedWrapper = $(`
				<div class="unified-workspace-view" data-persistent="true">
					<!-- Top Section: Navigation Breadcrumb -->
					<div class="workspace-breadcrumb" style="margin-bottom: 15px; padding: 10px 15px; background: #f8f9fa; border-radius: 6px;">
						<div style="display: flex; align-items: center; gap: 10px;">
							<span id="breadcrumb-text" style="font-weight: 500; color: #495057;">Timecard Management Dashboard</span>
							<button class="btn btn-sm btn-outline-secondary ml-auto" id="back-to-dashboard" style="display: none;">
								<i class="fa fa-arrow-left mr-1"></i>Back to Dashboard
							</button>
						</div>
					</div>

					<!-- Dynamic Content Container -->
					<div class="dynamic-content-container">
						<!-- Dashboard View -->
						<div id="dashboard-view" class="content-section">
							<!-- Header Section -->
							<div class="workspace-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px;">
								<div>
									<h3 style="margin: 0; color: #495057;">Timecard Management</h3>
									<p style="margin: 0; color: #6c757d; font-size: 14px;">Manage your timecards and employee data</p>
								</div>
								<div style="display: flex; gap: 10px;">
									<button class="btn btn-success" id="upload-timecard-btn">
										<i class="fa fa-upload mr-2"></i>Upload Timecard Sheet
									</button>
								</div>
							</div>

							<!-- Original Workspace Content -->
							<div class="original-workspace-section" data-persistent="true" style="margin-bottom: 30px; min-height: 300px;">
								<div class="editor-js-container" data-persistent="true" style="min-height: 250px;">
									<div id="editorjs" class="desk-page page-main-content" data-persistent="true"></div>
								</div>
								<div class="workspace-footer" data-persistent="true" style="margin-top: 20px; border-top: 1px solid #e9ecef; display: flex; gap: 10px; background: #fafbfc;">
									<button data-label="New" class="btn btn-default ellipsis btn-new-workspace">
										<svg class="es-icon es-line icon-xs" style="" aria-hidden="true">
											<use class="" href="#es-line-add"></use>
										</svg>
										<span class="hidden-xs" data-label="New">${__("New")}</span>
									</button>
									<button class="btn btn-default btn-sm btn-edit-workspace" data-label="Edit">
										<svg class="es-icon es-line icon-xs" style="" aria-hidden="true">
											<use class="" href="#es-line-edit"></use>
										</svg>
										<span class="hidden-xs" data-label="Edit">${__("Edit")}</span>
									</button>
								</div>
							</div>

							<!-- Timecard Sheets Section -->
							<div class="timecard-sheets-section">
								<div class="card">
									<div class="card-header">
										<h5 class="mb-0">
											<i class="fa fa-table mr-2"></i>Timecard Sheets
										</h5>
									</div>
									<div class="card-body">
										<div id="timecard-sheet-list" style="min-height: 400px; max-height: 600px; overflow-y: auto;">
											<div class="text-center py-3">
												<div class="spinner-border text-primary" role="status">
													<span class="sr-only">Loading...</span>
												</div>
												<p class="mt-2 text-muted">Loading timecard data...</p>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>

						<!-- DocType View Container -->
						<div id="doctype-view" class="content-section" style="display: none;">
							<div class="doctype-header" style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 20px; padding: 15px; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
								<div style="display: flex; align-items: center; gap: 15px;">
									<h4 id="doctype-title" style="margin: 0; color: #495057;"></h4>
									<div class="doctype-actions" style="display: flex; gap: 10px;">
										<button class="btn btn-primary btn-sm" id="new-doc-btn">
											<i class="fa fa-plus mr-1"></i>New
										</button>
										<button class="btn btn-outline-secondary btn-sm" id="refresh-doctype-btn">
											<i class="fa fa-refresh mr-1"></i>Refresh
										</button>
									</div>
								</div>
							</div>
							
							<!-- Search and Filter Bar -->
							<div class="doctype-filters" style="margin-bottom: 20px; padding: 15px; background: white; border-radius: 8px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
								<div class="row">
									<div class="col-md-6">
										<input type="text" class="form-control" id="doctype-search" placeholder="Search records...">
									</div>
								</div>
							</div>

							<!-- DocType Content Container -->
							<div id="doctype-content" class="doctype-content" style="background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); min-height: 500px;">
								<!-- Dynamic content will be loaded here -->
							</div>
						</div>
					</div>
				</div>
			`).appendTo(main_section);

			this.layout_initialized = true;
			console.log("Layout initialized successfully");

			// Setup event handlers
			this.setup_unified_event_handlers(unifiedWrapper);
			
			// Start content persistence monitoring
			this.startContentPersistence();
			
			// Load content with proper sequencing
			this.loadContentSequentially();

		} catch (error) {
			console.error('Error setting up unified layout:', error);
			this.layout_initialized = false;
			this.retryInitialization();
		}
	}

	// CRITICAL: Stop default Frappe workspace refresh mechanisms
	stopDefaultRefresh() {
		try {
			// Disable default workspace auto-refresh
			if (this.refresh_interval) {
				clearInterval(this.refresh_interval);
				this.refresh_interval = null;
			}

			// Override default refresh methods
			const originalRefresh = this.refresh;
			this.refresh = () => {
				if (!this.disable_default_refresh) {
					originalRefresh.call(this);
				} else {
					console.log("Default refresh disabled - maintaining custom layout");
				}
			};

			// Prevent chart widget errors from affecting the layout
			window.addEventListener('error', (e) => {
				if (e.message.includes('NaN') || e.message.includes('Expected length')) {
					e.preventDefault();
					console.warn('Chart rendering error prevented:', e.message);
					return false;
				}
			});

		} catch (error) {
			console.error('Error stopping default refresh:', error);
		}
	}

	// Content persistence monitoring
	startContentPersistence() {
		// Clear any existing interval
		if (this.content_persistence_interval) {
			clearInterval(this.content_persistence_interval);
		}

		// Monitor content every 2 seconds and restore if missing
		this.content_persistence_interval = setInterval(() => {
			this.ensureContentPersistence();
		}, 2000);
	}

	ensureContentPersistence() {
		try {
			const wrapper = $('.unified-workspace-view[data-persistent="true"]');
			const editorContainer = $('#editorjs[data-persistent="true"]');
			const workspaceSection = $('.original-workspace-section[data-persistent="true"]');
			
			// If main wrapper is missing, recreate layout
			if (wrapper.length === 0) {
				console.warn("Main wrapper disappeared, recreating layout...");
				this.layout_initialized = false;
				this.setup_unified_layout();
				return;
			}

			// If editor container is missing or hidden, restore it
			if (editorContainer.length === 0 || !editorContainer.is(':visible')) {
				console.warn("Editor container missing or hidden, restoring...");
				this.restoreEditorContainer();
			}

			// Ensure workspace section is visible
			if (workspaceSection.length > 0 && !workspaceSection.is(':visible')) {
				console.warn("Workspace section hidden, restoring visibility...");
				workspaceSection.show().css({
					'display': 'block !important',
					'visibility': 'visible !important',
					'opacity': '1 !important'
				});
			}

		} catch (error) {
			console.error('Error in content persistence check:', error);
		}
	}

	restoreEditorContainer() {
		try {
			let editorContainer = $('#editorjs');
			
			if (editorContainer.length === 0) {
				// Recreate editor container
				const workspaceSection = $('.original-workspace-section');
				if (workspaceSection.length > 0) {
					const editorJsContainer = workspaceSection.find('.editor-js-container');
					if (editorJsContainer.length > 0) {
						editorJsContainer.html('<div id="editorjs" class="desk-page page-main-content" data-persistent="true"></div>');
						editorContainer = $('#editorjs');
					}
				}
			}

			// Ensure visibility
			editorContainer.show().css({
				'display': 'block !important',
				'visibility': 'visible !important',
				'opacity': '1 !important',
				'min-height': '200px'
			});

			// Re-initialize editor if needed
			if (!this.editor_ready && this.content) {
				setTimeout(() => {
					this.prepare_editorjs();
				}, 100);
			}

		} catch (error) {
			console.error('Error restoring editor container:', error);
		}
	}

	async loadContentSequentially() {
		try {
			console.log("Loading content sequentially...");
			
			// Step 1: Load original workspace content
			await this.load_original_workspace_content();
			
			// Step 2: Wait a bit then load timecard data
			setTimeout(() => {
				this.load_timecard_data();
			}, 300);

			// Step 3: Ensure content visibility
			setTimeout(() => {
				this.ensureContentVisibility();
			}, 600);

		} catch (error) {
			console.error('Error loading content:', error);
			this.retryContentLoad();
		}
	}

	retryContentLoad() {
		setTimeout(() => {
			if (!this.content_loaded) {
				console.log("Retrying content load...");
				this.loadContentSequentially();
			}
		}, 1000);
	}

	setup_unified_event_handlers(wrapper) {
		// Back to dashboard button
		wrapper.find('#back-to-dashboard').off('click').on('click', () => {
			this.showDashboardView();
		});

		// Upload button
		wrapper.find('#upload-timecard-btn').off('click').on('click', () => {
			try {
				frappe.model.with_doctype('Data Import', () => {
					const doc = frappe.model.get_new_doc('Data Import');
					doc.reference_doctype = "Timecard Sheet";
					doc.import_type = "Insert New Records";
					frappe.set_route('Form', 'Data Import', doc.name);
				});
			} catch (error) {
				console.error('Error opening Data Import:', error);
				frappe.show_alert({
					message: 'Error opening Data Import',
					indicator: 'red'
				});
			}
		});

		// DocType view controls with error handling
		wrapper.find('#new-doc-btn').off('click').on('click', () => {
			if (this.current_doctype_view) {
				this.openDocTypeForm(this.current_doctype_view, 'new');
			}
		});

		wrapper.find('#refresh-doctype-btn').off('click').on('click', () => {
			if (this.current_doctype_view) {
				this.loadDocTypeView(this.current_doctype_view);
			}
		});

		// Search functionality with debouncing
		let searchTimeout;
		wrapper.find('#doctype-search').off('input').on('input', (e) => {
			clearTimeout(searchTimeout);
			searchTimeout = setTimeout(() => {
				this.filterDocTypeRecords($(e.target).val());
			}, 300);
		});

		// Enhanced workspace buttons
		wrapper.find(".btn-new-workspace").off('click').on("click", () => {
			try {
				this.initialize_new_page(true);
				setTimeout(() => {
					this.ensureContentVisibility();
				}, 500);
			} catch (error) {
				console.error('Error creating new workspace:', error);
			}
		});

		this.bind_edit_button_handler(wrapper);
	}

	showDashboardView() {
		console.log("Showing dashboard view...");
		
		try {
			$('#dashboard-view').show();
			$('#doctype-view').hide();
			$('#back-to-dashboard').hide();
			$('#breadcrumb-text').text('Timecard Management Dashboard');
			
			this.active_section = 'workspace';
			this.current_doctype_view = null;
			
			// Update sidebar selection safely
			if (this.sidebar) {
				this.sidebar.find('.section-item.active').removeClass('active');
			}

			// Ensure content is visible
			this.ensureContentVisibility();
		} catch (error) {
			console.error('Error showing dashboard view:', error);
		}
	}

	loadDocTypeView(doctype) {
		if (!doctype) {
			console.error('No doctype provided');
			return;
		}

		console.log(`Loading DocType view for: ${doctype}`);
		
		try {
			this.current_doctype_view = doctype;
			this.active_section = 'doctype';
			
			// Update UI
			$('#dashboard-view').hide();
			$('#doctype-view').show();
			$('#back-to-dashboard').show();
			$('#breadcrumb-text').text(`${doctype} Management`);
			$('#doctype-title').text(doctype);
			
			// Load DocType data with error handling
			this.fetchDocTypeData(doctype);
		} catch (error) {
			console.error('Error loading DocType view:', error);
			frappe.show_alert({
				message: `Error loading ${doctype}`,
				indicator: 'red'
			});
		}
	}

	fetchDocTypeData(doctype) {
		const container = $('#doctype-content').empty();
		
		// Show loading state
		container.html(`
			<div class="text-center py-5">
				<div class="spinner-border text-primary" role="status">
					<span class="sr-only">Loading...</span>
				</div>
				<p class="mt-3 text-muted">Loading ${doctype} records...</p>
			</div>
		`);

		// Enhanced error handling for meta loading
		frappe.model.with_doctype(doctype, () => {
			try {
				const meta = frappe.get_meta(doctype);
				if (!meta) {
					throw new Error(`Meta not found for ${doctype}`);
				}

				const fields = meta.fields.filter(f => 
					!f.hidden && 
					!["Section Break", "Column Break", "Tab Break"].includes(f.fieldtype) &&
					f.in_list_view
				).slice(0, 8);

				const fieldnames = ["name", "modified", ...fields.map(f => f.fieldname)];

				// Fetch data with enhanced error handling
				frappe.call({
					method: "frappe.client.get_list",
					args: {
						doctype: doctype,
						fields: fieldnames,
						order_by: "modified desc",
						limit_page_length: 50
					},
					callback: (r) => {
						try {
							const data = r.message || [];
							this.renderDocTypeTable(doctype, data, fields);
						} catch (error) {
							console.error('Error rendering DocType table:', error);
							this.showDocTypeError(container, doctype, 'Error rendering data');
						}
					},
					error: (err) => {
						console.error('Error fetching DocType data:', err);
						this.showDocTypeError(container, doctype, 'Error loading records');
					}
				});
			} catch (error) {
				console.error('Error processing DocType meta:', error);
				this.showDocTypeError(container, doctype, 'Error processing DocType');
			}
		}).catch(error => {
			console.error('Error loading DocType meta:', error);
			this.showDocTypeError(container, doctype, 'Error loading DocType metadata');
		});
	}

	showDocTypeError(container, doctype, message) {
		container.html(`
			<div class="text-center py-5">
				<i class="fa fa-exclamation-triangle fa-3x text-warning mb-3"></i>
				<h5 class="text-muted">${message}</h5>
				<p class="text-muted">There was an error loading ${doctype}. Please try again.</p>
				<button class="btn btn-primary" onclick="frappe.workspace.loadDocTypeView('${doctype}')">
					<i class="fa fa-refresh mr-2"></i>Retry
				</button>
			</div>
		`);
	}

	renderDocTypeTable(doctype, data, fields) {
		const container = $('#doctype-content').empty();

		if (!data.length) {
			container.html(`
				<div class="text-center py-5">
					<i class="fa fa-inbox fa-3x text-muted mb-3"></i>
					<h5 class="text-muted">No ${doctype} records found</h5>
					<p class="text-muted">Start by creating your first ${doctype.toLowerCase()} record.</p>
					<button class="btn btn-primary" onclick="frappe.workspace.openDocTypeForm('${doctype}', 'new')">
						<i class="fa fa-plus mr-2"></i>Create New ${doctype}
					</button>
				</div>
			`);
			return;
		}

		// Build the table with proper error handling
		let tableHTML = `
			<div class="table-responsive p-3">
				<table class="table table-hover table-striped doctype-table" data-doctype="${doctype}">
					<thead class="thead-light">
						<tr>
							<th width="50">#</th>
							<th>Name</th>
							${fields.map(f => `<th>${__(f.label)}</th>`).join("")}
							<th width="120">Modified</th>
							<th width="100">Actions</th>
						</tr>
					</thead>
					<tbody>
						${data.map((row, i) => `
							<tr class="doctype-row" data-name="${row.name}" data-doctype="${doctype}">
								<td><strong>${i + 1}</strong></td>
								<td>
									<a href="#" class="text-primary font-weight-bold record-link" data-name="${row.name}">
										${row.name}
									</a>
								</td>
								${fields.map(f => `<td>${this.format_field_value(row[f.fieldname], f) || "-"}</td>`).join("")}
								<td class="text-muted small">
									${frappe.datetime.str_to_user(row.modified) || ""}
								</td>
								<td>
									<div class="btn-group btn-group-sm">
										<button class="btn btn-outline-primary btn-view" data-name="${row.name}" title="View">
											<i class="fa fa-eye"></i>
										</button>
										<button class="btn btn-outline-secondary btn-edit" data-name="${row.name}" title="Edit">
											<i class="fa fa-edit"></i>
										</button>
									</div>
								</td>
							</tr>
						`).join("")}
					</tbody>
				</table>
			</div>
		`;

		container.html(tableHTML);
		this.setupDocTypeTableInteractions(doctype);
	}

	setupDocTypeTableInteractions(doctype) {
		// Enhanced event handlers with proper error handling
		$('.record-link').off('click').on('click', (e) => {
			e.preventDefault();
			const docname = $(e.currentTarget).data('name');
			if (docname) {
				this.openDocTypeForm(doctype, docname);
			}
		});

		$('.doctype-row').off('click').on('click', function(e) {
			if ($(e.target).closest('.btn-group').length) return;
			const docname = $(this).data('name');
			if (docname && frappe.workspace) {
				frappe.workspace.openDocTypeForm(doctype, docname);
			}
		});

		$('.btn-view, .btn-edit').off('click').on('click', function(e) {
			e.stopPropagation();
			const docname = $(this).data('name');
			if (docname && frappe.workspace) {
				frappe.workspace.openDocTypeForm(doctype, docname);
			}
		});
	}

	openDocTypeForm(doctype, docname) {
		console.log(`Opening ${doctype} form for: ${docname}`);
		
		try {
			if (docname === 'new') {
				frappe.new_doc(doctype);
			} else {
				frappe.set_route('Form', doctype, docname);
			}
		} catch (error) {
			console.error('Error opening form:', error);
			frappe.show_alert({
				message: 'Error opening form',
				indicator: 'red'
			});
		}
	}

	filterDocTypeRecords(searchTerm) {
		try {
			const searchLower = searchTerm.toLowerCase();
			$('.doctype-table tbody tr').each(function() {
				const rowText = $(this).text().toLowerCase();
				$(this).toggle(rowText.includes(searchLower));
			});
		} catch (error) {
			console.error('Error filtering records:', error);
		}
	}

	bind_edit_button_handler(wrapper) {
		wrapper.find(".btn-edit-workspace").off('click').on("click", async () => {
			try {
				if (!this.editor || !this.editor.readOnly) return;

				this.is_read_only = false;
				this.toggle_hidden_workspaces(true);

				await this.editor.readOnly.toggle();

				this.editor.isReady.then(() => {
					this.page.main.addClass("edit-mode");
					this.initialize_editorjs_undo();
					this.setup_customization_buttons(this._page);
					this.show_sidebar_actions();
					this.make_blocks_sortable();

					// Ensure content stays visible
					setTimeout(() => {
						this.ensureContentVisibility();
					}, 300);
				});
			} catch (error) {
				console.error('Error toggling edit mode:', error);
			}
		});
	}

	ensureContentVisibility() {
		console.log("Ensuring content visibility...");
		
		try {
			const wrapper = this.page.main.find(".unified-workspace-view");
			if (!wrapper.length) {
				console.log("Unified workspace view not found, recreating...");
				this.layout_initialized = false;
				this.setup_unified_layout();
				return;
			}

			// Force visibility of key elements with data-persistent attribute
			const elements = [
				'.original-workspace-section[data-persistent="true"]',
				'#editorjs[data-persistent="true"]',
				'.workspace-footer[data-persistent="true"]',
				'.editor-js-container[data-persistent="true"]'
			];

			elements.forEach(selector => {
				const element = $(selector);
				if (element.length) {
					element.css({
						'display': 'block !important',
						'visibility': 'visible !important',
						'opacity': '1 !important'
					}).show();
				}
			});

			// Re-initialize editor if needed and content exists
			if (!this.editor_ready && this.content && $('#editorjs').length) {
				console.log("Re-initializing editor...");
				setTimeout(() => {
					this.prepare_editorjs();
				}, 100);
			}

			this.content_loaded = true;
			console.log("Content visibility ensured");

		} catch (error) {
			console.error('Error ensuring content visibility:', error);
		}
	}

	async load_original_workspace_content() {
		console.log("Loading original workspace content...");
		
		try {
			// Wait for pages to be loaded
			await this.waitForPages();
			
			let page = this.get_page_to_show();
			
			if (this.all_pages && this.all_pages.length) {
				let pages = page.public && this.public_pages.length ? this.public_pages : this.private_pages;
				let current_page = pages.filter((p) => p.title == page.name)[0];
				
				if (current_page) {
					console.log("Found current page:", current_page.title);
					this._page = current_page;
					this.content = current_page && JSON.parse(current_page.content || '{}');
					
					if (this.content) {
						this.add_custom_cards_in_content();
					}

					// Load or use cached page data
					if (this.pages && this.pages[current_page.name]) {
						console.log("Using cached page data");
						this.page_data = this.pages[current_page.name];
						await this.prepare_editorjs();
					} else {
						console.log("Fetching page data...");
						await this.get_data(current_page);
						await this.prepare_editorjs();
					}

					this.setup_actions(page);
					
					// Ensure content visibility after loading
					setTimeout(() => {
						this.ensureContentVisibility();
					}, 200);
				}
			} else {
				throw new Error("No pages found");
			}
		} catch (error) {
			console.error('Error loading workspace content:', error);
			// Retry after delay
			setTimeout(() => {
				this.load_original_workspace_content();
			}, 1000);
		}
	}

	waitForPages() {
		return new Promise((resolve, reject) => {
			let attempts = 0;
			const maxAttempts = 20;
			
			const checkPages = () => {
				attempts++;
				if (this.all_pages && this.all_pages.length > 0) {
					resolve();
				} else if (attempts >= maxAttempts) {
					reject(new Error('Pages not loaded after maximum attempts'));
				} else {
					setTimeout(checkPages, 200);
				}
			};
			
			checkPages();
		});
	}

	async prepare_editorjs() {
		console.log("Preparing EditorJS...");
		
		try {
			// CRITICAL: Improved editor destruction with proper error handling
			if (this.editor) {
				console.log("Destroying existing editor...");
				try {
					// Check if editor has destroy method and is properly initialized
					if (this.editor.destroy && typeof this.editor.destroy === 'function') {
						await this.editor.destroy();
					} else if (this.editor.isReady) {
						// Wait for editor to be ready before destroying
						await this.editor.isReady;
						if (this.editor.destroy && typeof this.editor.destroy === 'function') {
							await this.editor.destroy();
						}
					}
				} catch (destroyError) {
					console.warn('Error destroying editor, proceeding anyway:', destroyError);
					// Clear editor reference even if destroy failed
					this.editor = null;
				}
			}
			
			// Ensure editor container exists
			let editorContainer = $('#editorjs');
			if (editorContainer.length === 0) {
				console.warn("Editor container not found, creating...");
				const workspaceSection = $('.original-workspace-section .editor-js-container');
				if (workspaceSection.length > 0) {
					workspaceSection.html('<div id="editorjs" class="desk-page page-main-content" data-persistent="true"></div>');
					editorContainer = $('#editorjs');
				} else {
					throw new Error("Cannot create editor container - workspace section not found");
				}
			}

			// Call parent method with enhanced error handling
			try {
				await super.prepare_editorjs();
				this.editor_ready = true;
				console.log("EditorJS prepared successfully");
			} catch (editorError) {
				console.error('Error in super.prepare_editorjs:', editorError);
				// Try alternative initialization
				await this.fallback_editor_initialization();
			}
			
			// Multiple attempts to ensure content visibility
			const ensureVisibility = () => {
				this.ensureContentVisibility();
			};

			setTimeout(ensureVisibility, 100);
			setTimeout(ensureVisibility, 300);
			setTimeout(ensureVisibility, 600);
			setTimeout(ensureVisibility, 1000);

		} catch (error) {
			console.error('Error preparing EditorJS:', error);
			// Try fallback initialization
			setTimeout(() => {
				this.fallback_editor_initialization();
			}, 1000);
		}
	}

	async fallback_editor_initialization() {
		console.log("Attempting fallback editor initialization...");
		
		try {
			// Ensure we have the content and container
			if (!this.content || !$('#editorjs').length) {
				console.error("Missing content or container for fallback initialization");
				return;
			}

			// Simple fallback: just ensure the container is visible with basic content
			const editorContainer = $('#editorjs');
			editorContainer.css({
				'display': 'block',
				'visibility': 'visible',
				'opacity': '1',
				'min-height': '200px',
				'padding': '20px',
				'background': '#fafbfc',
				'border': '1px solid #e9ecef',
				'border-radius': '6px'
			});

			// Add basic content if editor failed to initialize
			if (!this.editor_ready) {
				editorContainer.html(`
					<div class="editor-fallback">
						<p><strong>Workspace Content</strong></p>
						<p>Editor is loading... If this persists, the workspace is still functional for navigation.</p>
						<div class="mt-3">
							<button class="btn btn-sm btn-outline-primary" onclick="location.reload()">
								<i class="fa fa-refresh mr-1"></i>Refresh Page
							</button>
						</div>
					</div>
				`);
			}

			this.editor_ready = true;
			
		} catch (error) {
			console.error('Fallback editor initialization failed:', error);
		}
	}

	load_timecard_data() {
		console.log("Loading timecard data...");
		
		// Enhanced error handling for timecard data loading with chart error prevention
		frappe.call({
			method: "frappe.client.get_list",
			args: {
				doctype: "Timecard Sheet",
				fields: ["name"],
				limit: 1
			},
			callback: () => {
				frappe.model.with_doctype("Timecard Sheet", () => {
					try {
						const meta = frappe.get_meta("Timecard Sheet");
						if (!meta) {
							throw new Error("Timecard Sheet meta not found");
						}

						const fields = meta.fields.filter(f =>
							!f.hidden && 
							!["Section Break", "Column Break", "Tab Break"].includes(f.fieldtype) &&
							f.fieldtype !== "Table" // Exclude table fields that might cause issues
						).slice(0, 6); // Limit fields to prevent rendering issues

						const fieldnames = ["name", "modified", ...fields.map(f => f.fieldname)];
						
						frappe.call({
							method: "frappe.client.get_list",
							args: {
								doctype: "Timecard Sheet",
								fields: fieldnames,
								order_by: "modified desc",
								limit_page_length: 20
							},
							callback: (r) => {
								try {
									const data = r.message || [];
									this.render_timecard_table(data, fields);
								} catch (error) {
									console.error('Error rendering timecard table:', error);
									this.show_timecard_error('Error rendering timecard data');
								}
							},
							error: (err) => {
								console.error('Error fetching timecard data:', err);
								this.show_timecard_error('Error loading timecard data');
							}
						});
					} catch (error) {
						console.error('Error with timecard meta:', error);
						this.show_timecard_error('Error processing timecard metadata');
					}
				}).catch(error => {
					console.error('Error loading timecard meta:', error);
					this.show_timecard_error('Error loading timecard metadata');
				});
			},
			error: (err) => {
				console.error('Error checking timecard access:', err);
				this.show_timecard_error('Error accessing timecard data');
			}
		});
	}

	show_timecard_error(message) {
		const container = $('#timecard-sheet-list');
		container.html(`
			<div class="text-center py-5">
				<i class="fa fa-exclamation-triangle fa-3x text-warning mb-3"></i>
				<h5 class="text-muted">${message}</h5>
				<p class="text-muted">There was an error loading timecard data. Please try again.</p>
				<button class="btn btn-primary" onclick="frappe.workspace.load_timecard_data()">
					<i class="fa fa-refresh mr-2"></i>Retry
				</button>
			</div>
		`);
	}

	render_timecard_table(data, fields) {
		const container = $('#timecard-sheet-list').empty();

		if (!data.length) {
			container.html(`
				<div class="text-center py-5">
					<i class="fa fa-inbox fa-3x text-muted mb-3"></i>
					<h5 class="text-muted">No Timecard Sheets found</h5>
					<p class="text-muted">Start by uploading or creating your first timecard sheet.</p>
					<button class="btn btn-primary" onclick="frappe.set_route('Form', 'Timecard Sheet', 'new')">
						<i class="fa fa-plus mr-2"></i>Create New Timecard
					</button>
				</div>
			`);
			return;
		}

		// Build enhanced table with search and filters
		let tableHTML = `
			<div class="timecard-table-wrapper">
				<div class="table-controls mb-3">
					<div class="row">
						<div class="col-md-6">
							<input type="text" class="form-control" id="timecard-search" placeholder="Search timecards...">
						</div>
						<div class="col-md-6 text-right">
							<button class="btn btn-outline-primary btn-sm" id="refresh-timecards">
								<i class="fa fa-refresh mr-1"></i>Refresh
							</button>
						</div>
					</div>
				</div>
				
				<div class="table-responsive">
					<table class="table table-hover table-striped" id="timecard-table">
						<thead class="thead-light">
							<tr>
								<th width="40">#</th>
								<th>Name</th>
								${fields.map(f => `<th>${__(f.label)}</th>`).join("")}
								<th width="120">Modified</th>
								<th width="100">Actions</th>
							</tr>
						</thead>
						<tbody>
							${data.map((row, i) => `
								<tr class="timecard-row" data-name="${row.name}">
									<td><strong>${i + 1}</strong></td>
									<td>
										<a href="#" class="text-primary font-weight-bold timecard-link" data-name="${row.name}">
											${row.name}
										</a>
									</td>
									${fields.map(f => `<td>${this.format_field_value(row[f.fieldname], f) || "-"}</td>`).join("")}
									<td class="text-muted small">
										${frappe.datetime.str_to_user(row.modified) || ""}
									</td>
									<td>
										<div class="btn-group btn-group-sm">
											<button class="btn btn-outline-primary btn-view" data-name="${row.name}" title="View">
												<i class="fa fa-eye"></i>
											</button>
											<button class="btn btn-outline-secondary btn-edit" data-name="${row.name}" title="Edit">
												<i class="fa fa-edit"></i>
											</button>
										</div>
									</td>
								</tr>
							`).join("")}
						</tbody>
					</table>
				</div>
			</div>
		`;

		container.html(tableHTML);
		this.setup_table_interactions();
	}

	format_field_value(value, field) {
		if (!value && value !== 0) return "";
		
		try {
			switch (field.fieldtype) {
				case "Date":
					return frappe.datetime.str_to_user(value);
				case "Currency":
					return format_currency ? format_currency(value) : value;
				case "Percent":
					return value + "%";
				case "Check":
					return value ? "Yes" : "No";
				case "Select":
					return value;
				case "Link":
					return value;
				default:
					// Truncate long text values
					const strValue = String(value);
					return strValue.length > 50 ? strValue.substring(0, 47) + "..." : strValue;
			}
		} catch (error) {
			console.error('Error formatting field value:', error);
			return String(value);
		}
	}

	setup_table_interactions() {
		// Enhanced search functionality with debouncing
		let searchTimeout;
		$('#timecard-search').off('input').on('input', function() {
			clearTimeout(searchTimeout);
			searchTimeout = setTimeout(() => {
				const searchTerm = $(this).val().toLowerCase();
				$('#timecard-table tbody tr').each(function() {
					const rowText = $(this).text().toLowerCase();
					$(this).toggle(rowText.includes(searchTerm));
				});
			}, 300);
		});

		// Refresh button with error handling
		$('#refresh-timecards').off('click').on('click', () => {
			try {
				this.load_timecard_data();
			} catch (error) {
				console.error('Error refreshing timecards:', error);
				frappe.show_alert({
					message: 'Error refreshing timecard data',
					indicator: 'red'
				});
			}
		});

		// Enhanced row click handlers
		$('.timecard-row').off('click').on('click', function(e) {
			if ($(e.target).closest('.btn-group').length) return;
			const docname = $(this).data('name');
			if (docname) {
				frappe.set_route('Form', 'Timecard Sheet', docname);
			}
		});

		// Timecard link handlers
		$('.timecard-link').off('click').on('click', function(e) {
			e.preventDefault();
			const docname = $(this).data('name');
			if (docname) {
				frappe.set_route('Form', 'Timecard Sheet', docname);
			}
		});

		// Action button handlers with error handling
		$('.btn-view').off('click').on('click', function(e) {
			e.stopPropagation();
			try {
				const docname = $(this).data('name');
				if (docname) {
					frappe.set_route('Form', 'Timecard Sheet', docname);
				}
			} catch (error) {
				console.error('Error opening timecard view:', error);
			}
		});

		$('.btn-edit').off('click').on('click', function(e) {
			e.stopPropagation();
			try {
				const docname = $(this).data('name');
				if (docname) {
					frappe.set_route('Form', 'Timecard Sheet', docname);
				}
			} catch (error) {
				console.error('Error opening timecard edit:', error);
			}
		});
	}

	// Enhanced show method with better error handling and persistence
	show() {
		console.log("Showing workspace...");
		
		try {
			if (!this.all_pages) {
				console.log("Pages not loaded yet, waiting...");
				setTimeout(() => this.show(), 200);
				return;
			}
			
			let page = this.get_page_to_show();
			this.page.set_title(__(page.name));
			this.update_selected_sidebar(this.current_page, false);
			this.update_selected_sidebar(page, true);
			
			// Ensure layout is setup
			if (!this.layout_initialized) {
				this.setup_unified_layout();
			}
			
			// Re-enable content persistence monitoring
			if (!this.content_persistence_interval) {
				this.startContentPersistence();
			}
			
			// Ensure content persists after show
			setTimeout(() => {
				this.ensureContentVisibility();
			}, 300);

		} catch (error) {
			console.error('Error showing workspace:', error);
			// Retry once
			setTimeout(() => {
				this.show();
			}, 500);
		}
	}

	// Enhanced sidebar configuration with error handling
	getSidebarSections() {
		return [
			{
				title: "Employee Management",
				expanded: true,
				items: [
					{ title: "Employee Type"},
					{ title: "Customers"},
					{ title: "Employee Assignment"},
					{ title: "Shift Rate" },
					{ title: "Employee"}
				]
			},
			{
				title: "Timecard & Invoicing",
				expanded: true,
				items: [
					{ title: "Timecard Sheet"},
					{ title: "Timecard Invoice Detail"},
					{ title: "Sales Invoice"}
				]
			},
			{
				title: "Reports",
				expanded: false,
				items: [
					{ title: "Sales Register", count: null, type: "Report" },
					{ title: "Accounts Receivable", count: null, type: "Report" }
				]
			}
		];
	}

	make_sidebar() {
		try {
			if (!this.sidebar) {
				this.sidebar = $('<div class="workspace-sidebar"></div>').appendTo(this.page.sidebar.empty());
			}

			this.sidebar.empty();
			this.addCustomStyles();

			this.sidebar_sections.forEach((section) => {
				this.buildCollapsibleSection(section);
			});

			this.buildOriginalSidebar();

			const selected = this.sidebar.find(".selected");
			if (selected.length && !frappe.dom.is_element_in_viewport(selected)) {
				selected[0].scrollIntoView();
			}

			this.remove_sidebar_skeleton();
		} catch (error) {
			console.error('Error creating sidebar:', error);
		}
	}

	addCustomStyles() {
		if (!$('#workspace-sidebar-styles').length) {
			$(`<style id="workspace-sidebar-styles">
				.unified-workspace-view {
					animation: fadeIn 0.5s ease-in;
				}

				@keyframes fadeIn {
					from { opacity: 0; transform: translateY(10px); }
					to { opacity: 1; transform: translateY(0); }
				}

				.workspace-breadcrumb {
					border: 1px solid #e9ecef;
				}

				.content-section {
					transition: all 0.3s ease;
				}

				.workspace-header {
					border: 1px solid #e9ecef;
				}

				/* CRITICAL: Force visibility styles with higher specificity */
				.unified-workspace-view .original-workspace-section[data-persistent="true"] {
					background: white !important;
					border-radius: 8px !important;
					padding: 20px 20px 0 20px !important;
					box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
					display: block !important;
					visibility: visible !important;
					opacity: 1 !important;
					position: relative !important;
					z-index: 1 !important;
				}

				.unified-workspace-view .workspace-footer[data-persistent="true"] {
					margin-top: 20px !important;
					border-top: 1px solid #e9ecef !important;
					display: flex !important;
					gap: 10px !important;
					background: #fafbfc !important;
					border-radius: 0 0 8px 8px !important;
					margin-left: -20px !important;
					margin-right: -20px !important;
					margin-bottom: 0 !important;
					visibility: visible !important;
					padding: 15px 20px !important;
				}

				.unified-workspace-view .editor-js-container[data-persistent="true"] {
					min-height: 250px !important;
					margin-bottom: 0 !important;
					display: block !important;
					visibility: visible !important;
					opacity: 1 !important;
					position: relative !important;
				}

				.unified-workspace-view #editorjs[data-persistent="true"] {
					min-height: 200px !important;
					display: block !important;
					visibility: visible !important;
					opacity: 1 !important;
					position: relative !important;
					z-index: 1 !important;
				}

				/* Enhanced loading states */
				.loading-placeholder {
					background: linear-gradient(90deg, #f0f0f0 25%, transparent 37%, #f0f0f0 63%);
					background-size: 400% 100%;
					animation: loading 1.4s ease infinite;
				}

				@keyframes loading {
					0% { background-position: 100% 50%; }
					100% { background-position: -100% 50%; }
				}

				/* Prevent chart errors from affecting layout */
				.chart-container {
					min-height: 200px;
					position: relative;
				}

				.chart-container canvas {
					max-width: 100% !important;
					height: auto !important;
				}

				/* Error boundary styles */
				.editor-fallback {
					padding: 20px;
					text-align: center;
					background: #f8f9fa;
					border: 1px dashed #dee2e6;
					border-radius: 6px;
					color: #6c757d;
				}

				/* Sidebar styles */
				.collapsible-section {
					margin-bottom: 8px;
					border-radius: 6px;
					overflow: hidden;
				}

				.section-header {
					background: #f8f9fa;
					padding: 12px 16px;
					cursor: pointer;
					border: 1px solid #e9ecef;
					display: flex;
					align-items: center;
					justify-content: space-between;
					font-weight: 600;
					color: #495057;
					transition: all 0.2s ease;
				}

				.section-header:hover {
					background: #e9ecef;
				}

				.section-header.collapsed {
					border-bottom: none;
				}

				.section-title {
					display: flex;
					align-items: center;
					gap: 8px;
				}

				.section-toggle {
					transition: transform 0.2s ease;
				}

				.section-toggle.collapsed {
					transform: rotate(-90deg);
				}

				.section-content {
					border: 1px solid #e9ecef;
					border-top: none;
					background: white;
					overflow: hidden;
					transition: max-height 0.3s ease;
				}

				.section-content.collapsed {
					max-height: 0;
					border: none;
				}

				.section-item {
					padding: 10px 20px;
					border-bottom: 1px solid #f1f3f4;
					cursor: pointer;
					display: flex;
					justify-content: space-between;
					align-items: center;
					transition: background-color 0.2s ease;
				}

				.section-item:hover {
					background: #f8f9fa;
				}

				.section-item:last-child {
					border-bottom: none;
				}

				.section-item.active {
					background: #e3f2fd;
					border-left: 3px solid #2196f3;
				}

				.item-title {
					color: #666;
					font-size: 14px;
				}

				/* Responsive adjustments */
				@media (max-width: 768px) {
					.doctype-header {
						flex-direction: column;
						gap: 15px;
					}
					
					.doctype-filters .row {
						flex-direction: column;
					}
					
					.doctype-filters .col-md-6 {
						margin-bottom: 10px;
					}
				}
			</style>`).appendTo('head');
		}
	}

	buildCollapsibleSection(section) {
		try {
			const sectionElement = $(`
				<div class="collapsible-section">
					<div class="section-header ${section.expanded ? '' : 'collapsed'}" data-section="${section.title}">
						<div class="section-title">
							<span>${section.title}</span>
						</div>
						<i class="fa fa-chevron-down section-toggle ${section.expanded ? '' : 'collapsed'}"></i>
					</div>
					<div class="section-content ${section.expanded ? '' : 'collapsed'}" style="max-height: ${section.expanded ? (section.items.length * 45) + 'px' : '0'}">
						${section.items.map(item => `
							<div class="section-item" data-item="${item.title}" data-type="${item.type || 'List'}">
								<span class="item-title">${item.title}</span>
								${item.count ? `<span class="badge badge-secondary">${item.count}</span>` : ''}
							</div>
						`).join('')}
					</div>
				</div>
			`);

			sectionElement.find('.section-header').off('click').on('click', (e) => {
				this.toggleSection($(e.currentTarget));
			});

			sectionElement.find('.section-item').off('click').on('click', (e) => {
				this.selectItem($(e.currentTarget));
			});

			this.sidebar.append(sectionElement);
		} catch (error) {
			console.error('Error building collapsible section:', error);
		}
	}

	toggleSection(header) {
		try {
			const isCollapsed = header.hasClass('collapsed');
			const content = header.next('.section-content');
			const toggle = header.find('.section-toggle');
			
			if (isCollapsed) {
				header.removeClass('collapsed');
				toggle.removeClass('collapsed');
				content.removeClass('collapsed');
				content.css('max-height', (content.find('.section-item').length * 45) + 'px');
			} else {
				header.addClass('collapsed');
				toggle.addClass('collapsed');
				content.addClass('collapsed');
				content.css('max-height', '0');
			}
		} catch (error) {
			console.error('Error toggling section:', error);
		}
	}

	selectItem(item) {
		try {
			// Update visual selection
			this.sidebar.find('.section-item.active').removeClass('active');
			item.addClass('active');

			const itemTitle = item.data('item');
			const itemType = item.data('type') || 'List';

			const doctypeName = this.getDocTypeName(itemTitle);
			
			if (itemType === 'Report') {
				console.log(`Opening report: ${doctypeName}`);
				frappe.set_route('query-report', doctypeName);
			} else {
				console.log(`Loading DocType inline: ${doctypeName}`);
				this.loadDocTypeView(doctypeName);
			}
		} catch (error) {
			console.error('Navigation error:', error);
			frappe.show_alert({
				message: `Could not load ${item.data('item')}`,
				indicator: 'red'
			});
		}
	}

	getDocTypeName(displayName) {
		const doctypeMap = {
			"Employee Type": "Employee Type",
			"Customers": "Customer",
			"Employee Assignment": "Employee Assignment",
			"Shift Rate": "Shift Rate",
			"Employee": "Employee",
			"Timecard Sheet": "Timecard Sheet",
			"Timecard Invoice Detail": "Timecard Invoice Detail",
			"Sales Invoice": "Sales Invoice",
			"Sales Register": "Sales Register",
			"Accounts Receivable": "Accounts Receivable"
		};

		return doctypeMap[displayName] || displayName;
	}

	buildOriginalSidebar() {
		try {
			if (this.sidebar_categories && this.sidebar_categories.length > 0) {
				// Add a separator
				this.sidebar.append(`<div class="standard-sidebar-section">`);
				
				this.sidebar_categories.forEach((category) => {
					let root_pages = this.public_pages.filter(
						(page) => page.parent_page == "" || page.parent_page == null
					);
					if (category.id !== "Public") {
						root_pages = this.private_pages.filter(
							(page) => page.parent_page == "" || page.parent_page == null
						);
					}
					root_pages = root_pages.uniqBy((d) => d.title);
					this.build_sidebar_section(category, root_pages);
				});
				
				this.sidebar.append(`</div>`);
			}
		} catch (error) {
			console.error('Error building original sidebar:', error);
		}
	}

	// Cleanup method to be called when navigating away
	cleanup() {
		try {
			console.log("Cleaning up workspace...");
			
			// Clear content persistence interval
			if (this.content_persistence_interval) {
				clearInterval(this.content_persistence_interval);
				this.content_persistence_interval = null;
			}

			// Re-enable default refresh if needed
			this.disable_default_refresh = false;

			// Clean up editor
			this.editor_ready = false;
			
		} catch (error) {
			console.error('Error during cleanup:', error);
		}
	}
};