frappe.listview_settings['Timecard Sheet'] = {
    hide_name_column: true,
    onload: function (listview) {
        listview.setup_side_bar = function() {
        };

        listview.$page.find('.layout-side-section').remove();
        listview.page.add_inner_button("Generate Invoice Detail", function() {
            // First get the latest timecard record to extract pay period
            frappe.call({
                method: 'frappe.client.get_list',
                args: {
                    doctype: 'Timecard Sheet',
                    fields: ['name', 'pay_period'],
                    order_by: 'creation desc',
                    limit_start: 0,
                    limit_page_length: 1
                },
                callback: function(r) {
                    if (r.message && r.message.length > 0) {
                        let latest_record = r.message[0];
                        let pay_period = latest_record.pay_period;
                        
                        // Parse the pay period to extract start and end dates
                        let dates = parsePayPeriod(pay_period);
                        
                        if (!dates) {
                            frappe.msgprint({
                                title: __('Error'),
                                message: __('Unable to parse pay period from latest record'),
                                indicator: 'red'
                            });
                            return;
                        }
                        
                        // Show confirmation dialog
                        let dialog = new frappe.ui.Dialog({
                            title: __('Process Timecard Records'),
                            fields: [
                                {
                                    fieldtype: 'HTML',
                                    fieldname: 'confirmation_message',
                                    options: `<p>Process records for pay period: <strong>${pay_period}</strong>?</p>`
                                }
                            ],
                            primary_action_label: __('Yes'),
                            secondary_action_label: __('No'),
                            primary_action: function() {
                                dialog.hide();
                                generateInvoiceDetails(dates.start_date, dates.end_date);
                            },
                            secondary_action: function() {
                                dialog.hide();
                                // Navigate to Employee Assignment doctype
                                frappe.set_route('List', 'Employee Assignment');
                            }
                        });
                        
                        dialog.show();
                        
                    } else {
                        frappe.msgprint({
                            title: __('No Records'),
                            message: __('No timecard records found'),
                            indicator: 'orange'
                        });
                    }
                },
                error: function() {
                    frappe.msgprint({
                        title: __('Error'),
                        message: __('Error fetching latest timecard record'),
                        indicator: 'red'
                    });
                }
            });
        });
        
        // Function to parse pay period string
        function parsePayPeriod(pay_period) {
            if (!pay_period) return null;
            
            // Expected format: "2025-06-29 to 2025-07-12"
            let parts = pay_period.split(' to ');
            if (parts.length !== 2) return null;
            
            return {
                start_date: parts[0].trim(),
                end_date: parts[1].trim()
            };
        }
        
        // Function to generate invoice details
        function generateInvoiceDetails(start_date, end_date) {
            frappe.show_alert({
                message: __('Generating invoice details...'),
                indicator: 'blue'
            });
            
            frappe.call({
                method: 'klhealth.klhealth.methods.generate_invoice_details',
                args: {
                    'start_date': start_date,
                    'end_date': end_date
                },
                freeze: true,
                freeze_message: __('Processing timecards and generating invoice details...'),
                callback: function(r) {
                    if (r.message && r.message.success) {
                        frappe.msgprint({
                            title: __('Invoice Generation Complete'),
                            message: r.message.message,
                            indicator: 'green'
                        });
                        
                    setTimeout(function() {
                        frappe.set_route('List', 'Timecard Invoice Detail');
                    }, 2000);
                    } else {
                        frappe.msgprint({
                            title: __('Error'),
                            message: r.message ? r.message.message : __('Error occurred while generating invoice details'),
                            indicator: 'red'
                        });
                    }
                },
                error: function(r) {
                    frappe.msgprint({
                        title: __('Error'),
                        message: __('Error occurred while generating invoice details'),
                        indicator: 'red'
                    });
                }
            });
        }
    }
};