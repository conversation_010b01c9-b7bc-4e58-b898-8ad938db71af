// Enhanced Stripe payment button for custom DocType "Collective Invoices" with overdue handling
frappe.ui.form.on('Collective Invoices', {
    refresh: function(frm) {
        // Check for overdue status on form load
        check_overdue_status(frm);
        
        // Show button for saved documents (no submit required)
        if (!frm.doc.__islocal) {
            frm.add_custom_button(__('Pay with Stripe'), function() {
                pay_with_stripe(frm);
            }, __('Actions'));
        }

        // Enhanced Send Invoice Email button with hardcoded template
        if (!frm.doc.__islocal) {
            frm.add_custom_button(__('Send Invoice Email'), function () {
                // Get customer email as default
                let default_email = '';
                if (frm.doc.customer) {
                    frappe.call({
                        method: 'frappe.client.get_value',
                        args: {
                            doctype: 'Customer',
                            filters: { name: frm.doc.customer },
                            fieldname: 'email_id'
                        },
                        callback: function(customer_r) {
                            if (customer_r.message && customer_r.message.email_id) {
                                default_email = customer_r.message.email_id;
                            }
                            show_email_dialog(default_email, frm);
                        }
                    });
                } else {
                    show_email_dialog(default_email, frm);
                }
            }, __('Actions'));
        }
    },
    
    // Also check when due_date field changes
    due_date: function(frm) {
        check_overdue_status(frm);
    }
});

function show_email_dialog(default_email, frm) {
    // Show dialog to input recipient email only (template is hardcoded)
    let dialog = new frappe.ui.Dialog({
        title: __('Send Invoice Email'),
        fields: [
            {
                fieldtype: 'Data',
                fieldname: 'recipient_email',
                label: __('Recipient Email'),
                reqd: 1,
                default: default_email,
                description: __('Email address to send the invoice to')
            },
            {
                fieldtype: 'Check',
                fieldname: 'attach_pdf',
                label: __('Attach PDF'),
                default: 1,
                read_only: 1,
                description: __('PDF invoice will be automatically attached')
            },
            {
                fieldtype: 'Check',
                fieldname: 'attach_csv',
                label: __('Attach Timecard CSV'),
                default: 1,
                read_only: 1,
                description: __('CSV file with timecard details will be automatically attached')
            },
            {
                fieldtype: 'HTML',
                fieldname: 'email_preview',
                label: __('Email Preview'),
                options: `
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 10px;">
                        <strong>Subject:</strong> Collective Invoice - ${frm.doc.name}<br><br>
                        <strong>Message Preview:</strong><br>
                        <div style="margin-top: 10px; padding: 10px; background: white; border-left: 3px solid #007bff;">
                            Dear ${frm.doc.customer || 'Customer'},<br><br>
                            Please find attached your collective invoice for the period: ${frm.doc.date_range || 'N/A'}.<br><br>
                            Invoice Details:<br>
                            - Invoice Number: ${frm.doc.name}<br>
                            - Total Amount: ${frappe.format(frm.doc.total_amount, {fieldtype: 'Currency'}) || 'N/A'}<br>
                            - Due Date: ${frm.doc.due_date || 'N/A'}<br><br>
                            Please process the payment at your earliest convenience.<br><br>
                            Thank you for your business!<br><br>
                            Best Regards,<br>
                            ${frappe.defaults.get_global_default("company") || 'Your Company'}
                        </div>
                    </div>
                `
            }
        ],
        primary_action_label: __('Send Email'),
        primary_action: function(values) {
            if (values.recipient_email) {
                // Validate email format
                const email_regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!email_regex.test(values.recipient_email)) {
                    frappe.msgprint({
                        title: __('Invalid Email'),
                        message: __('Please enter a valid email address.'),
                        indicator: 'red'
                    });
                    return;
                }

                // Show loading message
                frappe.show_alert({
                    message: __('Sending email...'),
                    indicator: 'blue'
                });

                // Define the hardcoded email template
                const email_template = {
                    subject: `Collective Invoice - ${frm.doc.name}`,
                    message: `Dear ${frm.doc.customer || 'Customer'},

Please find attached your collective invoice for the period: ${frm.doc.date_range || 'N/A'}.

Invoice Details:
- Invoice Number: ${frm.doc.name}
- Total Amount: ${frappe.format(frm.doc.total_amount, {fieldtype: 'Currency'}) || 'N/A'}
- Due Date: ${frm.doc.due_date || 'N/A'}

Please process the payment at your earliest convenience.

Thank you for your business!

Best Regards,
${frappe.defaults.get_global_default("company") || 'Your Company'}`
                };

                // Send email with hardcoded template, PDF attachment, and CSV attachment
                frappe.call({
                    method: 'klhealth.methods.send_email.send_collective_invoice_email_with_csv',
                    args: {
                        collective_invoice: frm.doc.name,
                        recipient_email: values.recipient_email,
                        email_subject: email_template.subject,
                        email_message: email_template.message
                    },
                    callback: function(response) {
                        if (response.message && response.message.success) {
                            frappe.msgprint({
                                title: __('Success'),
                                message: __('Email sent successfully to {0}', [values.recipient_email]),
                                indicator: 'green'
                            });
                        } else {
                            frappe.msgprint({
                                title: __('Error'),
                                message: response.message && response.message.error ? response.message.error : __('Failed to send email. Please try again.'),
                                indicator: 'red'
                            });
                        }
                    },
                    error: function(err) {
                        frappe.msgprint({
                            title: __('Error'),
                            message: __('An error occurred while sending the email.'),
                            indicator: 'red'
                        });
                        console.error('Email sending error:', err);
                    }
                });
                dialog.hide();
            }
        }
    });
    
    dialog.show();
}

function check_overdue_status(frm) {
    if (!frm.doc.due_date || frm.doc.__islocal) {
        return;
    }
    
    const today = frappe.datetime.get_today();
    const due_date = frm.doc.due_date;
    
    // Check if past due date and not already overdue
    if (frappe.datetime.get_diff(today, due_date) > 0 && frm.doc.status !== 'Overdue') {
        // Get late fee percentage from KL Settings
        frappe.call({
            method: "frappe.client.get",
            args: {
                doctype: "KL Settings",
                name: "KL Settings"
            },
            callback: function(r) {
                if (r.message && r.message.late_fee) {
                    apply_late_fee(frm, r.message.late_fee);
                } else {
                    frappe.msgprint(__('Late fee percentage not found in KL Settings'));
                }
            }
        });
    }
}

function apply_late_fee(frm, late_fee) {
    // Check if late fee already exists to avoid duplicates
    const existing_late_fee = frm.doc.items?.find(item => item.type === 'Late fee');
    if (existing_late_fee) {
        return; // Already has late fee
    }
    
    // Calculate late fee amount
    const total_amount = frm.doc.total_amount || 0;
    const late_fee_amount = total_amount * (late_fee / 100);
    
    // Add new row to items table (assuming table field name is 'items')
    const new_row = frm.add_child('reference_invoices');
    new_row.type = 'Late fee';
    new_row.grand_total = late_fee_amount;
    new_row.outstanding = late_fee_amount;

    // Update totals
    const new_total = total_amount + late_fee_amount;
    frm.set_value('total_amount', new_total);
    
    // Update status to Overdue
    frm.set_value('status', 'Overdue');
    
    // Refresh the items table
    frm.refresh_field('reference_invoices');
    
    // Save the document
    frm.save().then(() => {
        frappe.show_alert({
            message: __('Late fee applied and status updated to Overdue'),
            indicator: 'orange'
        });
    });
}

function pay_with_stripe(frm) {
    // Show loading message
    frappe.show_alert({
        message: __('Creating Stripe payment session...'),
        indicator: 'blue'
    });
    
    frappe.call({
        method: "klhealth.klhealth.stripe.create_stripe_url",
        args: {
            sales_invoice: frm.doc.name
        },
        callback: function(r) {
            if (r.message && r.message.url) {
                // Success - redirect to Stripe
                frappe.show_alert({
                    message: __('Redirecting to Stripe...'),
                    indicator: 'green'
                });
                
                // Open in new tab
                window.open(r.message.url, '_blank');
                
                // Reload the document to get any updates
                frm.reload_doc();
            } else {
                // Error handling
                frappe.show_alert({
                    message: __('Failed to create Stripe payment link'),
                    indicator: 'red'
                });
            }
        },
        error: function(r) {
            frappe.show_alert({
                message: __('Error connecting to Stripe'),
                indicator: 'red'
            });
            console.error('Stripe error:', r);
        }
    });
}