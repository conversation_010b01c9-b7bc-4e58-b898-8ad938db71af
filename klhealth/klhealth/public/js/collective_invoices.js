frappe.ui.form.on('Collective Invoices', {
    refresh: function(frm) {
        check_overdue_status(frm);

        if (!frm.doc.__islocal) {
            frm.add_custom_button(__('Send Invoice Email'), function () {
                let default_email = '';
                if (frm.doc.customer) {
                    frappe.call({
                        method: 'frappe.client.get_value',
                        args: {
                            doctype: 'Customer',
                            filters: { name: frm.doc.customer },
                            fieldname: 'email_id'
                        },
                        callback: function(customer_r) {
                            if (customer_r.message && customer_r.message.email_id) {
                                default_email = customer_r.message.email_id;
                            }
                            show_email_dialog(default_email, frm);
                        }
                    });
                } else {
                    show_email_dialog(default_email, frm);
                }
            }, __('Actions'));
        }

        if (!frm.doc.__islocal && frappe.user.has_role('System Manager')) {
            frm.add_custom_button(__('Debug CSV Generation'), function () {
                debug_csv_generation(frm);
            }, __('Debug'));
        }
    },
    
    due_date: function(frm) {
        check_overdue_status(frm);
    }
});

frappe.listview_settings['Collective Invoices'] = {
    onload: function(listview) {
        setTimeout(function() {
            if (listview && listview.page) {
                listview.page.sidebar && listview.page.sidebar.remove();
                listview.$page && listview.$page.find('.layout-side-section').hide();
                listview.$page && listview.$page.find('.layout-main-section').removeClass('col-md-9').addClass('col-md-12');
            }
        }, 100);
    }
};

function show_email_dialog(default_email, frm) {
    let dialog = new frappe.ui.Dialog({
        title: __('Send Invoice Email'),
        fields: [
            {
                fieldtype: 'Data',
                fieldname: 'recipient_email',
                label: __('Recipient Email'),
                reqd: 1,
                default: default_email,
                description: __('Email address to send the invoice to')
            },
            {
                fieldtype: 'Check',
                fieldname: 'attach_pdf',
                label: __('Attach PDF'),
                default: 1,
                read_only: 1,
                description: __('PDF invoice will be automatically attached')
            },
            {
                fieldtype: 'Check',
                fieldname: 'attach_csv',
                label: __('Attach Timecard CSV'),
                default: 1,
                read_only: 1,
                description: __('CSV file with timecard details will be automatically attached')
            },
            {
                fieldtype: 'HTML',
                fieldname: 'email_preview',
                label: __('Email Preview'),
                options: `
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 10px;">
                        <strong>Subject:</strong> Collective Invoice - ${frm.doc.name}<br><br>
                        <strong>Message Preview:</strong><br>
                        <div style="margin-top: 10px; padding: 10px; background: white; border-left: 3px solid #007bff;">
                            Dear ${frm.doc.customer || 'Customer'},<br><br>
                            Please find attached your collective invoice for the period: ${frm.doc.date_range || 'N/A'}.<br><br>
                            Invoice Details:<br>
                            - Invoice Number: ${frm.doc.name}<br>
                            - Total Amount: ${frappe.format(frm.doc.total_amount, {fieldtype: 'Currency'}) || 'N/A'}<br>
                            - Due Date: ${frm.doc.due_date || 'N/A'}<br><br>
                            Please process the payment at your earliest convenience.<br><br>
                            Thank you for your business!<br><br>
                            Best Regards,<br>
                            ${frappe.defaults.get_global_default("company") || 'Your Company'}
                        </div>
                    </div>
                `
            }
        ],
        primary_action_label: __('Send Email'),
        primary_action: function(values) {
            if (values.recipient_email) {
                const email_regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!email_regex.test(values.recipient_email)) {
                    frappe.msgprint({
                        title: __('Invalid Email'),
                        message: __('Please enter a valid email address.'),
                        indicator: 'red'
                    });
                    return;
                }

                frappe.show_alert({
                    message: __('Preparing email with CSV data...'),
                    indicator: 'blue'
                });

                const email_template = {
                    subject: `Collective Invoice - ${frm.doc.name}`,
                    message: `Dear ${frm.doc.customer || 'Customer'},

Please find attached your collective invoice for the period: ${frm.doc.date_range || 'N/A'}.

Invoice Details:
- Invoice Number: ${frm.doc.name}
- Total Amount: ${frappe.format(frm.doc.total_amount, {fieldtype: 'Currency'}) || 'N/A'}
- Due Date: ${frm.doc.due_date || 'N/A'}

Please process the payment at your earliest convenience.

Thank you for your business!

Best Regards,
${frappe.defaults.get_global_default("company") || 'Your Company'}`
                };

                generate_timecard_csv_and_send_email(frm, values.recipient_email, email_template);
                dialog.hide();
            }
        }
    });
    
    dialog.show();
}

function generate_timecard_csv_and_send_email(frm, recipient_email, email_template) {
    frappe.call({
        method: 'frappe.client.get_value',
        args: {
            doctype: 'KL Customers',
            filters: { company_name: frm.doc.customer },
            fieldname: 'location'
        },
        callback: function(customer_response) {
            if (!customer_response.message || !customer_response.message.location) {
                frappe.msgprint({
                    title: __('Warning'),
                    message: __('Could not find location for customer: {0}. Sending email without CSV.', [frm.doc.customer]),
                    indicator: 'orange'
                });
                send_email_without_csv(frm, recipient_email, email_template);
                return;
            }

            const customer_location = customer_response.message.location;
            
            const date_range = frm.doc.date_range;
            if (!date_range) {
                frappe.msgprint({
                    title: __('Warning'),
                    message: __('Date range not found in collective invoice. Sending email without CSV.'),
                    indicator: 'orange'
                });
                send_email_without_csv(frm, recipient_email, email_template);
                return;
            }

            const date_parts = date_range.split(' to ');
            if (date_parts.length !== 2) {
                frappe.msgprint({
                    title: __('Warning'),
                    message: __('Invalid date range format. Expected format: MM/DD/YY to MM/DD/YY. Sending email without CSV.'),
                    indicator: 'orange'
                });
                send_email_without_csv(frm, recipient_email, email_template);
                return;
            }

            const start_date_str = date_parts[0].trim();
            const end_date_str = date_parts[1].trim();

            console.log('Date range parsing:', {
                original_range: date_range,
                start_date_str: start_date_str,
                end_date_str: end_date_str,
                customer_location: customer_location
            });

            frappe.call({
                method: 'frappe.client.get_list',
                args: {
                    doctype: 'Timecard Invoice Detail',
                    filters: [
                        ['location', '=', customer_location]
                    ],
                    fields: ['*'],
                    limit_page_length: 0 
                },
                callback: function(timecard_response) {
                    if (!timecard_response.message || timecard_response.message.length === 0) {
                        frappe.msgprint({
                            title: __('Info'),
                            message: __('No timecard details found for location: {0}. Sending email without CSV.', [customer_location]),
                            indicator: 'yellow'
                        });
                        send_email_without_csv(frm, recipient_email, email_template);
                        return;
                    }

                    const filtered_records = filter_records_by_date_range(timecard_response.message, start_date_str, end_date_str);
                    
                    if (filtered_records.length === 0) {
                        frappe.msgprint({
                            title: __('Info'),
                            message: __('No timecard details found for the specified date range ({0}). Sending email without CSV.', [date_range]),
                            indicator: 'yellow'
                        });
                        send_email_without_csv(frm, recipient_email, email_template);
                        return;
                    }

                    console.log(`Found ${filtered_records.length} records after date filtering`);

                    const csv_data = generate_csv_from_timecard_data(filtered_records);
                    
                    frappe.call({
                        method: 'klhealth.methods.send_email.send_collective_invoice_email_with_csv',
                        args: {
                            collective_invoice: frm.doc.name,
                            recipient_email: recipient_email,
                            email_subject: email_template.subject,
                            email_message: email_template.message,
                            csv_data: csv_data,
                            csv_filename: `timecard_details_${frm.doc.name}.csv`
                        },
                        callback: function(response) {
                            if (response.message && response.message.success) {
                                frappe.msgprint({
                                    title: __('Success'),
                                    message: __('Email sent successfully to {0} with timecard CSV attached ({1} records)', [recipient_email, filtered_records.length]),
                                    indicator: 'green'
                                });
                            } else {
                                frappe.msgprint({
                                    title: __('Error'),
                                    message: response.message && response.message.error ? response.message.error : __('Failed to send email. Please try again.'),
                                    indicator: 'red'
                                });
                            }
                        },
                        error: function(err) {
                            frappe.msgprint({
                                title: __('Error'),
                                message: __('An error occurred while sending the email.'),
                                indicator: 'red'
                            });
                            console.error('Email sending error:', err);
                        }
                    });
                },
                error: function(err) {
                    frappe.msgprint({
                        title: __('Error'),
                        message: __('Failed to fetch timecard details'),
                        indicator: 'red'
                    });
                    console.error('Timecard fetch error:', err);
                }
            });
        },
        error: function(err) {
            frappe.msgprint({
                title: __('Error'),
                message: __('Failed to fetch customer location'),
                indicator: 'red'
            });
            console.error('Customer location fetch error:', err);
        }
    });
}

function filter_records_by_date_range(records, start_date_str, end_date_str) {
    const start_date = parse_date_for_comparison(start_date_str);
    const end_date = parse_date_for_comparison(end_date_str);
    
    if (!start_date || !end_date) {
        console.error('Could not parse date range:', start_date_str, 'to', end_date_str);
        return [];
    }
    
    console.log('Filtering records between:', start_date, 'and', end_date);
    
    const filtered_records = records.filter(record => {
        if (!record.date) {
            console.log('Record has no date:', record.name);
            return false;
        }
        
        const record_date = parse_date_for_comparison(record.date);
        if (!record_date) {
            console.log('Could not parse record date:', record.date, 'for record:', record.name);
            return false;
        }
        
        const in_range = record_date >= start_date && record_date <= end_date;
        if (in_range) {
            console.log('Including record:', record.name, 'with date:', record.date, '(parsed as:', record_date, ')');
        } else {
            console.log('Excluding record:', record.name, 'with date:', record.date, '(parsed as:', record_date, ') - outside range');
        }
        
        return in_range;
    });
    
    return filtered_records;
}

function parse_date_for_comparison(date_str) {
    try {
        if (!date_str) return null;
        
        date_str = date_str.toString().trim();
        
        if (date_str.includes('/')) {
            const parts = date_str.split('/');
            if (parts.length === 3) {
                let month = parseInt(parts[0]);
                let day = parseInt(parts[1]);
                let year = parseInt(parts[2]);
                
                if (year < 100) {
                    if (year <= 30) {
                        year += 2000;
                    } else {
                        year += 1900;
                    }
                }
                
                const date = new Date(year, month - 1, day);
                
                if (date.getFullYear() === year && date.getMonth() === month - 1 && date.getDate() === day) {
                    return date;
                }
            }
        }
        
        else if (date_str.includes('-')) {
            const date = new Date(date_str);
            if (!isNaN(date.getTime())) {
                return date;
            }
        }
        
        console.error('Unrecognized date format:', date_str);
        return null;
        
    } catch (e) {
        console.error('Error parsing date:', date_str, e);
        return null;
    }
}

function convert_date_format(date_str) {
    try {
        const parts = date_str.split('/');
        if (parts.length !== 3) return null;
        
        let month = parts[0].padStart(2, '0');
        let day = parts[1].padStart(2, '0');
        let year = parts[2];
        
        if (year.length === 2) {
            const currentYear = new Date().getFullYear();
            const currentCentury = Math.floor(currentYear / 100) * 100;
            const yearNum = parseInt(year);
            
            if (yearNum <= 30) {
                year = (currentCentury + yearNum).toString();
            } else {
                year = (currentCentury - 100 + yearNum).toString();
            }
        } else if (year.length === 4) {
            year = year;
        } else {
            return null;
        }
        
        if (parseInt(month) < 1 || parseInt(month) > 12) return null;
        if (parseInt(day) < 1 || parseInt(day) > 31) return null;
        
        return `${year}-${month}-${day}`;
    } catch (e) {
        console.error('Date conversion error:', e);
        return null;
    }
}

function generate_csv_from_timecard_data(timecard_data) {
    if (!timecard_data || timecard_data.length === 0) {
        return '';
    }
    
    const field_mapping = [
        { field: 'adp_employee_name', header: 'Employee Name' },
        { field: 'employee_type', header: 'Employee Type' },
        { field: 'location', header: 'Location' },
        { field: 'day', header: 'Day' },
        { field: 'date', header: 'Date' },
        { field: 'start_time', header: 'Start Time' },
        { field: 'end_time', header: 'End Time' },
        { field: 'regular_dec', header: 'Regular Hours' },
        { field: 'overtime_dec', header: 'Overtime Hours' },
        { field: 'total_hours_worked', header: 'Total Hours Worked' },
        { field: 'sd_category', header: 'SD Category' },
        { field: 'sd_range', header: 'SD Range' },
        { field: 'sd_time', header: 'SD Time' },
        { field: 'regular_rate', header: 'Regular Rate' },
        { field: 'overtime_rate', header: 'Overtime Rate' },
        { field: 'holiday_rate', header: 'Holiday Rate' },
        { field: 'sd_rate', header: 'SD Rate' },
        { field: 'regular_invoice_amount', header: 'Regular Amount' },
        { field: 'overtime_invoice_amount', header: 'Overtime Amount' },
        { field: 'holiday_invoice_amount', header: 'Holiday Amount' },
        { field: 'sd_invoice_amount', header: 'SD Amount' },
        { field: 'invoice_status', header: 'Status' },
        { field: 'total_invoice_amount', header: 'Total Amount' },
        { field: 'comment', header: 'Comment' }
    ];
    
    const csv_header = field_mapping.map(fm => fm.header).join(',');
    
    const csv_rows = timecard_data.map(row => {
        return field_mapping.map(fm => {
            let value = row[fm.field] || '';
            if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
                value = '"' + value.replace(/"/g, '""') + '"';
            }
            return value;
        }).join(',');
    });
    
    return [csv_header, ...csv_rows].join('\n');
}

function send_email_without_csv(frm, recipient_email, email_template) {
    frappe.call({
        method: 'klhealth.methods.send_email.send_collective_invoice_email_with_csv',
        args: {
            collective_invoice: frm.doc.name,
            recipient_email: recipient_email,
            email_subject: email_template.subject,
            email_message: email_template.message,
            csv_data: '',
            csv_filename: ''
        },
        callback: function(response) {
            if (response.message && response.message.success) {
                frappe.msgprint({
                    title: __('Success'),
                    message: __('Email sent successfully to {0} (no timecard data found for CSV)', [recipient_email]),
                    indicator: 'green'
                });
            } else {
                frappe.msgprint({
                    title: __('Error'),
                    message: response.message && response.message.error ? response.message.error : __('Failed to send email. Please try again.'),
                    indicator: 'red'
                });
            }
        },
        error: function(err) {
            frappe.msgprint({
                title: __('Error'),
                message: __('An error occurred while sending the email.'),
                indicator: 'red'
            });
            console.error('Email sending error:', err);
        }
    });
}

function debug_csv_generation(frm) {
    frappe.call({
        method: 'klhealth.methods.send_email.test_csv_generation',
        args: {
            collective_invoice: frm.doc.name
        },
        callback: function(response) {
            if (response.message) {
                const data = response.message;
                let message = `<strong>CSV Generation Debug Results:</strong><br><br>`;
                message += `<strong>Success:</strong> ${data.success}<br>`;
                message += `<strong>Customer:</strong> ${data.customer || 'N/A'}<br>`;
                message += `<strong>Location:</strong> ${data.location || 'N/A'}<br>`;
                message += `<strong>Date Range:</strong> ${data.date_range || 'N/A'}<br>`;
                message += `<strong>Parsed Start Date:</strong> ${data.parsed_start_date || 'N/A'}<br>`;
                message += `<strong>Parsed End Date:</strong> ${data.parsed_end_date || 'N/A'}<br>`;
                message += `<strong>Total Location Records:</strong> ${data.total_location_records || 0}<br>`;
                message += `<strong>CSV Generated:</strong> ${data.csv_generated}<br>`;
                message += `<strong>CSV Length:</strong> ${data.csv_length || 0} characters<br>`;
                
                if (data.error) {
                    message += `<strong>Error:</strong> ${data.error}<br>`;
                }
                
                if (data.csv_preview) {
                    message += `<br><strong>CSV Preview (first 500 chars):</strong><br>`;
                    message += `<pre style="background: #f8f9fa; padding: 10px; font-size: 12px; max-height: 200px; overflow-y: auto;">${data.csv_preview}</pre>`;
                }
                
                frappe.msgprint({
                    title: __('CSV Generation Debug'),
                    message: message,
                    indicator: data.success ? 'green' : 'red'
                });
            }
        },
        error: function(err) {
            frappe.msgprint({
                title: __('Debug Error'),
                message: __('Failed to run debug test'),
                indicator: 'red'
            });
            console.error('Debug error:', err);
        }
    });
}

function check_overdue_status(frm) {
    if (!frm.doc.due_date || frm.doc.__islocal) {
        return;
    }
    
    const today = frappe.datetime.get_today();
    const due_date = frm.doc.due_date;
    
    if (frappe.datetime.get_diff(today, due_date) > 0 && frm.doc.status !== 'Overdue') {
        frappe.call({
            method: "frappe.client.get",
            args: {
                doctype: "KL Settings",
                name: "KL Settings"
            },
            callback: function(r) {
                if (r.message && r.message.late_fee) {
                    apply_late_fee(frm, r.message.late_fee);
                } else {
                    frappe.msgprint(__('Late fee percentage not found in KL Settings'));
                }
            }
        });
    }
}

function apply_late_fee(frm, late_fee) {
    const existing_late_fee = frm.doc.reference_invoices?.find(item => item.type === 'Late fee');
    if (existing_late_fee) {
        return;
    }
    
    const total_amount = frm.doc.total_amount || 0;
    const late_fee_amount = total_amount * (late_fee / 100);
    
    const new_row = frm.add_child('reference_invoices');
    new_row.type = 'Late fee';
    new_row.grand_total = late_fee_amount;
    new_row.outstanding = late_fee_amount;

    const new_total = total_amount + late_fee_amount;
    frm.set_value('total_amount', new_total);
    
    frm.set_value('status', 'Overdue');
    
    frm.refresh_field('reference_invoices');
    
    frm.save().then(() => {
        frappe.show_alert({
            message: __('Late fee applied and status updated to Overdue'),
            indicator: 'orange'
        });
    });
}

