frappe.listview_settings['Timecard Invoice Detail'] = {
    hide_name_column: true,
    onload: function (listview) {
        listview.setup_side_bar = function() {
        };

        listview.$page.find('.layout-side-section').remove();
        
        // Add Generate Collection Invoices button
        listview.page.add_inner_button("Generate Collection Invoices", function() {
            // First get the latest timecard record to extract pay period
            frappe.call({
                method: 'frappe.client.get_list',
                args: {
                    doctype: 'Timecard Sheet',
                    fields: ['name', 'pay_period'],
                    order_by: 'creation desc',
                    limit_start: 0,
                    limit_page_length: 1
                },
                callback: function(r) {
                    if (r.message && r.message.length > 0) {
                        let latest_record = r.message[0];
                        let pay_period = latest_record.pay_period;
                        
                        // Parse the pay period to extract start and end dates
                        let dates = parsePayPeriod(pay_period);
                        
                        if (!dates) {
                            frappe.msgprint({
                                title: __('Error'),
                                message: __('Unable to parse pay period from latest timecard record'),
                                indicator: 'red'
                            });
                            return;
                        }
                        
                        // Show confirmation dialog
                        let dialog = new frappe.ui.Dialog({
                            title: __('Generate Collection Invoices'),
                            fields: [
                                {
                                    fieldtype: 'HTML',
                                    fieldname: 'confirmation_message',
                                    options: `<p>Generate collection invoices for pay period: <strong>${pay_period}</strong>?</p>`
                                }
                            ],
                            primary_action_label: __('Yes'),
                            secondary_action_label: __('No'),
                            primary_action: function() {
                                dialog.hide();
                                generateCollectionInvoices(dates.start_date, dates.end_date);
                            },
                            secondary_action: function() {
                                dialog.hide();
                                // Just close the dialog - no other functionality
                            }
                        });
                        
                        dialog.show();
                        setTimeout(function() {
                        frappe.set_route('List', 'Collective Invoices');
                        }, 2000);
                        
                    } else {
                        frappe.msgprint({
                            title: __('No Records'),
                            message: __('No timecard records found'),
                            indicator: 'orange'
                        });
                    }
                },
                error: function() {
                    frappe.msgprint({
                        title: __('Error'),
                        message: __('Error fetching latest timecard record'),
                        indicator: 'red'
                    });
                }
            });
        });
        
        // Function to parse pay period string
        function parsePayPeriod(pay_period) {
            if (!pay_period) return null;
            
            // Expected format: "2025-06-29 to 2025-07-12"
            let parts = pay_period.split(' to ');
            if (parts.length !== 2) return null;
            
            return {
                start_date: parts[0].trim(),
                end_date: parts[1].trim()
            };
        }
        
        // Function to convert date to DD/MM/YYYY format
        function convertToDD_MM_YYYY(dateStr) {
            if (!dateStr) return dateStr;
            
            // If it's in YYYY-MM-DD format, convert to DD/MM/YYYY
            if (dateStr.match(/^\d{4}-\d{2}-\d{2}$/)) {
                let parts = dateStr.split('-');
                return parts[2] + '/' + parts[1] + '/' + parts[0];
            }
            
            return dateStr; // Return as-is if different format
        }
        
        // Function to generate collection invoices
        function generateCollectionInvoices(start_date, end_date) {
            let formatted_start_date = convertToDD_MM_YYYY(start_date);
            let formatted_end_date = convertToDD_MM_YYYY(end_date);
            
            console.log('Original dates:', start_date, end_date);
            console.log('Formatted dates:', formatted_start_date, formatted_end_date);
            
            frappe.call({
                method: 'klhealth.klhealth.generate_invoices_from_timecard.generate_collection_invoices',
                args: {
                    'start_date': formatted_start_date,
                    'end_date': formatted_end_date
                },
                freeze: true,
                freeze_message: __('Processing timecard invoice details and generating collection invoices...'),
                callback: function(r) {
                    console.log('Response:', r);
                    
                    if (r.message && r.message.success) {
                        frappe.msgprint({
                            title: __('Collection Invoices Generated Successfully'),
                            message: r.message.message || __('Collection invoices have been generated successfully'),
                            indicator: 'green'
                        });
                        
                        setTimeout(function() {
                            location.reload();
                        }, 2000);
                    } else {
                        frappe.msgprint({
                            title: __('Error'),
                            message: r.message ? r.message.message : __('Error occurred while generating collection invoices'),
                            indicator: 'red'
                        });
                    }
                },
                error: function(r) {
                    frappe.msgprint({
                        title: __('Error'),
                        message: __('Error occurred while generating collection invoices'),
                        indicator: 'red'
                    });
                }
            });
        }
    }
};

frappe.ui.form.on('Timecard Invoice Detail', {
    bonus_amount: function (frm) {
        calculate_total_invoice_amount(frm);
        highlight_if_edited(frm);
    },

    onload: function (frm) {
        calculate_total_invoice_amount(frm);
        // Initialize original state on load
        initialize_original_state(frm);
    },

    refresh: function (frm) {
        calculate_total_invoice_amount(frm);
        // Restore original state and apply highlights on refresh
        initialize_original_state(frm);
        setTimeout(() => highlight_if_edited(frm), 500); // Delay to ensure DOM is ready

        // Add Clear Highlights button
        frm.add_custom_button(__('Clear Highlights'), function () {
            clear_all_highlights(frm);
        }, __('Actions'));
    },

    on_change: function (frm) {
        highlight_if_edited(frm);
    },

    validate: function (frm) {
        const edited = highlight_if_edited(frm);
        if (edited && (!frm.doc.comment || frm.doc.comment.trim() === "")) {
            frappe.throw(__('Please add a comment before saving changes.'));
        }
    },

    after_save: function (frm) {
        // Maintain original state after save to keep highlights persistent
        setTimeout(() => highlight_if_edited(frm), 500);
    }
});

function initialize_original_state(frm) {
    if (!frm.doc.name) return; // Don't initialize for new documents
    
    const storage_key = `timecard_original_${frm.doc.name}`;
    
    // Try to get original state from localStorage first
    let stored_original = localStorage.getItem(storage_key);
    
    if (stored_original && !frm.__original) {
        try {
            frm.__original = stored_original;
            console.log("Restored original state from localStorage");
        } catch (e) {
            console.log("Error parsing stored original state:", e);
        }
    }
    
    // If no stored original state, create one from current doc
    if (!frm.__original) {
        // Create a clean snapshot without system fields
        let clean_doc = {};
        Object.keys(frm.doc).forEach(key => {
            if (!key.startsWith('__') && key !== 'modified' && key !== 'modified_by') {
                clean_doc[key] = frm.doc[key];
            }
        });
        
        frm.__original = JSON.stringify(clean_doc);
        localStorage.setItem(storage_key, frm.__original);
        console.log("Created new original state");
    }
}

function calculate_total_invoice_amount(frm) {
    let bonus_amount = parseFloat(frm.doc.bonus_amount) || 0;
    let current_total = parseFloat(frm.doc.total_invoice_amount) || 0;

    let stored_original_total = frm.doc.__original_total_without_bonus;

    if (stored_original_total === undefined) {
        frm.doc.__original_total_without_bonus = current_total;
        stored_original_total = current_total;
    }

    let base_amount = parseFloat(stored_original_total) || 0;

    let regular_amount = parseFloat(frm.doc.regular_invoice_amount) || 0;
    let overtime_amount = parseFloat(frm.doc.overtime_invoice_amount) || 0;
    let sd_amount = parseFloat(frm.doc.sd_invoice_amount) || 0;
    let holiday_amount = parseFloat(frm.doc.holiday_invoice_amount) || 0;

    if (regular_amount || overtime_amount || sd_amount || holiday_amount) {
        base_amount = regular_amount + overtime_amount + sd_amount + holiday_amount;
        frm.doc.__original_total_without_bonus = base_amount;
    }

    let total_invoice_amount = base_amount + bonus_amount;
    frm.set_value('total_invoice_amount', total_invoice_amount);
}

function highlight_if_edited(frm) {
    if (!frm.__original || !frm.doc.name) return false;
    
    const original = JSON.parse(frm.__original);
    const modified = frm.doc;
    let is_any_edited = false;

    const storage_key = `timecard_edited_${frm.doc.name}`;
    let edited_fields = JSON.parse(localStorage.getItem(storage_key) || '{}');

    frm.meta.fields.forEach(field => {
        const fieldname = field.fieldname;
        if (!fieldname || !modified.hasOwnProperty(fieldname)) return;

        if (fieldname.startsWith('__') || fieldname === 'modified' || fieldname === 'modified_by') return;

        const original_value = original[fieldname];
        const current_value = modified[fieldname];

        const field_instance = frm.fields_dict[fieldname];
        if (!field_instance) return;

        const $wrapper = field_instance.$wrapper;
        const $input = $wrapper.find('.input-with-feedback.form-control, .form-control, input, textarea, select');
        const $control = $wrapper.find('.control-input');
        const icon_id = `restore-icon-${fieldname}`;

        if (original_value !== current_value) {
            is_any_edited = true;
            edited_fields[fieldname] = { original: original_value, current: current_value };

            if ($input.length) {
                $input.css({
                    "background-color": "rgba(255, 215, 0, 0.1)"
                });
                $input.addClass('persistent-highlight');
            }

            $wrapper.addClass('field-edited');

            if (!$control.find(`#${icon_id}`).length) {
                const original_display = original_value === null || original_value === undefined ? 'empty' : original_value;
                const $restore_icon = $(`
                    <span id="${icon_id}" title="Restore original value: ${original_display}"
                          style="cursor:pointer; margin-left:6px; color:#d9534f; font-size: 16px; font-weight: bold; position: relative; top: -2px;">
                        ↩
                    </span>
                `);
                $control.append($restore_icon);

                $restore_icon.on("click", function () {
                    frm.set_value(fieldname, original_value);
                    if ($input.length) {
                        $input.css({
                            "border": "",
                            "background-color": ""
                        });
                        $input.removeClass('persistent-highlight');
                    }
                    $wrapper.removeClass('field-edited');
                    $restore_icon.remove();
                    
                    delete edited_fields[fieldname];
                    localStorage.setItem(storage_key, JSON.stringify(edited_fields));
                    
                    setTimeout(() => highlight_if_edited(frm), 100);
                });
            }
        } else {
            if ($input.length) {
                $input.css({
                    "border": "",
                    "background-color": ""
                });
                $input.removeClass('persistent-highlight');
            }
            $wrapper.removeClass('field-edited');
            $control.find(`#${icon_id}`).remove();
            
            delete edited_fields[fieldname];
        }
    });

    localStorage.setItem(storage_key, JSON.stringify(edited_fields));

    return is_any_edited;
}

function clear_all_highlights(frm) {
    if (!frm.doc.name) return;
    
    const storage_key = `timecard_original_${frm.doc.name}`;
    const edited_key = `timecard_edited_${frm.doc.name}`;
    
    frm.meta.fields.forEach(field => {
        const fieldname = field.fieldname;
        const field_instance = frm.fields_dict[fieldname];
        if (!field_instance) return;

        const $wrapper = field_instance.$wrapper;
        const $input = $wrapper.find('.input-with-feedback.form-control, .form-control, input, textarea, select');
        const $control = $wrapper.find('.control-input');
        const icon_id = `restore-icon-${fieldname}`;

        if ($input.length) {
            $input.css({
                "border": "",
                "background-color": ""
            });
            $input.removeClass('persistent-highlight');
        }
        $wrapper.removeClass('field-edited');
        $control.find(`#${icon_id}`).remove();
    });

    let clean_doc = {};
    Object.keys(frm.doc).forEach(key => {
        if (!key.startsWith('__') && key !== 'modified' && key !== 'modified_by') {
            clean_doc[key] = frm.doc[key];
        }
    });
    
    frm.__original = JSON.stringify(clean_doc);
    localStorage.setItem(storage_key, frm.__original);
    localStorage.removeItem(edited_key);
    
    frappe.show_alert({
        message: __('All highlights cleared'),
        indicator: 'green'
    });
}

$(document).ready(function() {
    if (!$('#persistent-highlight-styles').length) {
        $('<style id="persistent-highlight-styles">')
            .prop("type", "text/css")
            .html(`
                .persistent-highlight {
                    background-color: rgba(255, 215, 0, 0.1) !important;
                }
                .persistent-highlight:focus {
                    background-color: rgba(255, 140, 0, 0.1) !important;
                }
                .field-edited {
                    position: relative;
                }
            `)
            .appendTo("head");
    }
});