app_name = "klhealth"
app_title = "Klhealth"
app_publisher = "Geofinity Solutions"
app_description = "Klhealth Customization"
app_email = "<EMAIL>"
app_license = "mit"

# Apps
# ------------------

# required_apps = []

# Each item in the list will be shown as an app in the apps page
# add_to_apps_screen = [
# 	{
# 		"name": "klhealth",
# 		"logo": "/assets/klhealth/logo.png",
# 		"title": "Klhealth",
# 		"route": "/klhealth",
# 		"has_permission": "klhealth.api.permission.has_app_permission"
# 	}
# ]

# Includes in <head>
# ------------------

# include js, css files in header of desk.html\


app_include_css = [
    "/assets/klhealth/css/global.css",
    "/assets/klhealth/css/custom-workspace.css"
]

app_include_js = "/assets/klhealth/js/global.js"

# include js, css files in header of web template
# web_include_css = "/assets/klhealth/css/klhealth.css"
# web_include_js = "/assets/klhealth/js/klhealth.js"

# include custom scss in every website theme (without file extension ".scss")
# website_theme_scss = "klhealth/public/scss/website"

# include js, css files in header of web form
# webform_include_js = {"doctype": "public/js/doctype.js"}
# webform_include_css = {"doctype": "public/css/doctype.css"}

# include js in page
# page_js = {"page" : "public/js/file.js"}

# include js in doctype views
doctype_list_js = {"Timecard Sheet" : "public/js/timecard_sheet.js",
                   "Timecard Invoice Detail" : "public/js/timecard_invoice_details.js",
                   "Sales Invoice" : "public/js/sales_invoice.js",
                     "Collective Invoices" : "public/js/collective_invoices.js"
                   }

doctype_js = {"Payment Entry" : "public/js/payment_entry.js"}               
# doctype_list_js = {"doctype" : "public/js/doctype_list.js"}
# doctype_tree_js = {"doctype" : "public/js/doctype_tree.js"}
# doctype_calendar_js = {"doctype" : "public/js/doctype_calendar.js"}

# Svg Icons
# ------------------
# include app icons in desk
# app_include_icons = "klhealth/public/icons.svg"

# Home Pages
# ----------

# application home page (will override Website Settings)
# home_page = "login"

# website user home page (by Role)
# role_home_page = {
# 	"Role": "home_page"
# }

# Generators
# ----------

# automatically create page for each record of this doctype
# website_generators = ["Web Page"]

# Jinja
# ----------

# add methods and filters to jinja environment
# jinja = {
# 	"methods": "klhealth.utils.jinja_methods",
# 	"filters": "klhealth.utils.jinja_filters"
# }

# Installation
# ------------

# before_install = "klhealth.install.before_install"
# after_install = "klhealth.install.after_install"

# Uninstallation
# ------------

# before_uninstall = "klhealth.uninstall.before_uninstall"
# after_uninstall = "klhealth.uninstall.after_uninstall"

# Integration Setup
# ------------------
# To set up dependencies/integrations with other apps
# Name of the app being installed is passed as an argument

# before_app_install = "klhealth.utils.before_app_install"
# after_app_install = "klhealth.utils.after_app_install"

# Integration Cleanup
# -------------------
# To clean up dependencies/integrations with other apps
# Name of the app being uninstalled is passed as an argument

# before_app_uninstall = "klhealth.utils.before_app_uninstall"
# after_app_uninstall = "klhealth.utils.after_app_uninstall"

# Desk Notifications
# ------------------
# See frappe.core.notifications.get_notification_config

# notification_config = "klhealth.notifications.get_notification_config"

# Permissions
# -----------
# Permissions evaluated in scripted ways

# permission_query_conditions = {
# 	"Event": "frappe.desk.doctype.event.event.get_permission_query_conditions",
# }
#
# has_permission = {
# 	"Event": "frappe.desk.doctype.event.event.has_permission",
# }

# DocType Class
# ---------------
# Override standard doctype classes

# override_doctype_class = {
# 	"ToDo": "custom_app.overrides.CustomToDo"
# }

# Document Events
# ---------------
# Hook on document methods and events

# doc_events = {
# 	"*": {
# 		"on_update": "method",
# 		"on_cancel": "method",
# 		"on_trash": "method"
# 	}
# }

# Scheduled Tasks
# ---------------

# scheduler_events = {
# 	"all": [
# 		"klhealth.tasks.all"
# 	],
# 	"daily": [
# 		"klhealth.tasks.daily"
# 	],
# 	"hourly": [
# 		"klhealth.tasks.hourly"
# 	],
# 	"weekly": [
# 		"klhealth.tasks.weekly"
# 	],
# 	"monthly": [
# 		"klhealth.tasks.monthly"
# 	],
# }

# Testing
# -------

# before_tests = "klhealth.install.before_tests"

# Overriding Methods
# ------------------------------
#
# override_whitelisted_methods = {
# 	"frappe.desk.doctype.event.event.get_events": "klhealth.event.get_events"
# }
#
# each overriding function accepts a `data` argument;
# generated from the base implementation of the doctype dashboard,
# along with any modifications made in other Frappe apps
# override_doctype_dashboards = {
# 	"Task": "klhealth.task.get_dashboard_data"
# }

# exempt linked doctypes from being automatically cancelled
#
# auto_cancel_exempted_doctypes = ["Auto Repeat"]

# Ignore links to specified DocTypes when deleting documents
# -----------------------------------------------------------

# ignore_links_on_delete = ["Communication", "ToDo"]

# Request Events
# ----------------
# before_request = ["klhealth.utils.before_request"]
# after_request = ["klhealth.utils.after_request"]

# Job Events
# ----------
# before_job = ["klhealth.utils.before_job"]
# after_job = ["klhealth.utils.after_job"]

# User Data Protection
# --------------------

# user_data_fields = [
# 	{
# 		"doctype": "{doctype_1}",
# 		"filter_by": "{filter_by}",
# 		"redact_fields": ["{field_1}", "{field_2}"],
# 		"partial": 1,
# 	},
# 	{
# 		"doctype": "{doctype_2}",
# 		"filter_by": "{filter_by}",
# 		"partial": 1,
# 	},
# 	{
# 		"doctype": "{doctype_3}",
# 		"strict": False,
# 	},
# 	{
# 		"doctype": "{doctype_4}"
# 	}
# ]

# Authentication and authorization
# --------------------------------

# auth_hooks = [
# 	"klhealth.auth.validate"
# ]

# Automatically update python controller files with type annotations for this app.
# export_python_type_annotations = True

# default_log_clearing_doctypes = {
# 	"Logging DocType Name": 30  # days to retain logs
# }

