<!DOCTYPE html>
<html>
<head>
    <title>Observation Widget Test</title>
    <style>
        .grouped-obs {
            border: 1px solid #ddd;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 10px;
            background-color: #f8f9fa;
        }
        .observs {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 5px 15px 5px 0;
            border-radius: 5px;
            background-color: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .bulk-action-buttons {
            display: flex;
            gap: 5px;
        }
        .btn {
            padding: 2px 6px;
            font-size: 9px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .obs-field {
            margin-bottom: 10px;
        }
        .text-muted {
            color: #6c757d;
        }
    </style>
</head>
<body>
    <h2>Enhanced Observation Widget with Bulk Actions</h2>
    
    <!-- Example of grouped observation with components -->
    <div class="CBC grouped-obs">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <b>
                <a href="/app/observation/CBC">
                    CBC (COMPLETE BLOOD COUNT)
                </a>
            </b>
            <div class="bulk-action-buttons">
                <button class="btn btn-success" id="bulk-approve-CBC">
                    Approve All
                </button>
                <button class="btn btn-danger" id="bulk-disapprove-CBC">
                    Disapprove All
                </button>
            </div>
        </div>
        
        <!-- Component observations -->
        <div class="observations-hemoglobin observs">
            <div class="obs-field">
                <div style="font-size:10px; padding-top:2px; margin-bottom:2px;">
                    <a href="/app/observation/hemoglobin" title="hemoglobin">Hemoglobin</a>
                </div>
            </div>
            
            <div class="obs-field">
                <div class="text-muted" style="font-size:10px; padding-top:2px; margin-bottom:2px;">
                    <a href="/app/specimen/SPEC001" title="SPEC001">SPEC001</a>
                </div>
                <div class="text-muted" style="font-size: 9px; font-weight: 500; color: #6c757d;">
                    Sample Type: Blood
                </div>
                <div class="text-muted" style="font-size: 8px;">
                    4th August 2025, 06:18 PM
                </div>
            </div>
            
            <div class="obs-field">
                <input type="text" value="12.5" style="width: 60px;">
                <div class="text-muted" style="font-size:8px; margin-top:-12px;">
                    4th August 2025, 06:18 PM
                </div>
            </div>
            
            <div class="obs-field">
                <div style="display:flex; flex-direction: column;">
                    <div style="font-size:10px; padding-top:2px; margin-bottom:2px;">
                        <strong>Unit:</strong> g/dL
                    </div>
                    <div class="text-muted" style="font-size:10px; padding-top:5px; font-weight: 500; color: #495057;">
                        <strong>Method:</strong> Spectrophotometry
                    </div>
                </div>
            </div>
            
            <div class="obs-field">
                <div style="display:flex;">
                    <div class="text-muted" style="font-size:10px; padding-top:2px; margin-bottom:2px;">
                        12.0 - 16.0 g/dL
                    </div>
                </div>
            </div>
            
            <div class="obs-field">
                <div style="float:right;">
                    <button class="btn btn-success" style="font-size:10px; padding: 4px 8px;">
                        Approve
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Another component observation -->
        <div class="observations-pcv observs">
            <div class="obs-field">
                <div style="font-size:10px; padding-top:2px; margin-bottom:2px;">
                    <a href="/app/observation/pcv" title="pcv">PCV</a>
                </div>
            </div>
            
            <div class="obs-field">
                <div class="text-muted" style="font-size:10px; padding-top:2px; margin-bottom:2px;">
                    <a href="/app/specimen/SPEC001" title="SPEC001">SPEC001</a>
                </div>
                <div class="text-muted" style="font-size: 9px; font-weight: 500; color: #6c757d;">
                    Sample Type: Blood
                </div>
                <div class="text-muted" style="font-size: 8px;">
                    4th August 2025, 06:18 PM
                </div>
            </div>
            
            <div class="obs-field">
                <input type="text" value="38" style="width: 60px;">
                <div class="text-muted" style="font-size:8px; margin-top:-12px;">
                    4th August 2025, 06:18 PM
                </div>
            </div>
            
            <div class="obs-field">
                <div style="display:flex; flex-direction: column;">
                    <div style="font-size:10px; padding-top:2px; margin-bottom:2px;">
                        <strong>Unit:</strong> %
                    </div>
                    <div class="text-muted" style="font-size:10px; padding-top:5px; font-weight: 500; color: #495057;">
                        <strong>Method:</strong> Microhematocrit
                    </div>
                </div>
            </div>
            
            <div class="obs-field">
                <div style="display:flex;">
                    <div class="text-muted" style="font-size:10px; padding-top:2px; margin-bottom:2px;">
                        36 - 46 %
                    </div>
                </div>
            </div>
            
            <div class="obs-field">
                <div style="float:right;">
                    <button class="btn btn-success" style="font-size:10px; padding: 4px 8px;">
                        Approve
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Mock bulk action handlers
        document.getElementById('bulk-approve-CBC').addEventListener('click', function() {
            alert('Bulk approve functionality would approve all CBC component observations');
        });
        
        document.getElementById('bulk-disapprove-CBC').addEventListener('click', function() {
            alert('Bulk disapprove functionality would disapprove all approved CBC component observations');
        });
    </script>
</body>
</html>
