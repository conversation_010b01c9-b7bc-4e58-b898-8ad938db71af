import frappe

def inpatient_record(doc, method):
    ipr_meta = frappe.get_meta("Inpatient Record")
    emergency_meta = frappe.get_meta("Emergency")

    if not ipr_meta.has_field("emergency") or not emergency_meta.has_field("inpatient_record"):
        return 

    if method == "on_update":
        if doc.emergency:
            emergency = frappe.get_doc("Emergency", doc.emergency)
            if not emergency.inpatient_record or emergency.inpatient_record != doc.name:
                emergency.inpatient_record = doc.name
                emergency.status = "Admitted"
                emergency.save(ignore_permissions=True)
                frappe.db.commit()
            else:
                # frappe.throw(f"Emergency record already has Inpatient Record.{doc.name} {emergency.inpatient_record} {emergency.inpatient_record != doc.name}")
                pass

    elif method == "on_cancel":
        if doc.emergency:
            current = frappe.db.get_value("Emergency", doc.emergency, "inpatient_record")
            if current == doc.name:
                try:
                    frappe.db.set_value("Emergency", doc.emergency, "inpatient_record", "")
                    frappe.db.commit()
                except Exception as e:
                    frappe.throw("Error while clearing Inpatient Record: {}".format(str(e)))
