import frappe

def vital_signs(doc, method):
    vital_meta = frappe.get_meta("Vital Signs")
    emergency_meta = frappe.get_meta("Emergency")

    if not vital_meta.has_field("emergency") or not emergency_meta.has_field("vitals"):
        return 

    if method == "on_submit":
        if doc.emergency:
            emergency = frappe.get_doc("Emergency", doc.emergency)
            if not emergency.vitals or emergency.vitals != doc.name:
                emergency.vitals = doc.name
                if emergency.status not in ["Under Treatment"]:
                    emergency.status = "Under Treatment"
                emergency.save(ignore_permissions=True)
                frappe.db.commit()
            else:
                frappe.throw("Emergency record already has vital signs.")

    elif method == "on_cancel":
        if doc.emergency:
            current = frappe.db.get_value("Emergency", doc.emergency, "vitals")
            if current == doc.name:
                try:
                    frappe.db.set_value("Emergency", doc.emergency, "vitals", "")
                    frappe.db.commit()
                except Exception as e:
                    frappe.throw("Error while clearing vital signs: {}".format(str(e)))
