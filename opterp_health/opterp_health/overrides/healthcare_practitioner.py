import frappe
from healthcare.healthcare.doctype.healthcare_practitioner.healthcare_practitioner import (
    HealthcarePractitioner as OldHealthcarePractitioner,
)


class HealthcarePractitioner(OldHealthcarePractitioner):
    def set_full_name(self):
        name_parts = [self.first_name, getattr(self, "middle_name", None), self.last_name]
        full_name = " ".join(filter(None, name_parts))
        if self.healthcare_practitioner_type == "Doctor":
            self.practitioner_name = f"Dr. {full_name}"
        else:
            self.practitioner_name = full_name
