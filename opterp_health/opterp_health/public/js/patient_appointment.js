frappe.ui.form.on("Patient Appointment", {
    refresh: function (frm) {
        frm.add_custom_button("Print Label", async () => {
            try {
                // Load QZ Tray if not already loaded
                if (typeof qz === "undefined") {
                    await new Promise((resolve, reject) => {
                        let script = document.createElement("script");
                        script.src = "/assets/js/qz-tray.js";  // <-- adjust if you serve it elsewhere
                        script.onload = resolve;
                        script.onerror = () => reject("Failed to load QZ Tray library");
                        document.head.appendChild(script);
                    });
                }

                // Connect to QZ Tray
                await qz.websocket.connect();

                // Find printers
                const printers = await qz.printers.find();
                console.log("Available printers:", printers);

                // Find printer with name containing "xlab"
                const printerName = printers.find(p => p.toLowerCase().includes("xlab"));
                if (!printerName) {
                    frappe.msgprint("Xlab printer not found. Available printers: <br>" + printers.join("<br>"));
                    return;
                }

                // Create print config
                const config = qz.configs.create(printerName);

                // Simple test ZPL data
                const zpl = [
                    "^XA",
                    "^FO50,50^A0N,40,40^FDTest Print^FS",
                    "^XZ"
                ];

                // Send print job
                await qz.print(config, zpl);

                frappe.msgprint("Print job sent successfully!");

            } catch (error) {
                frappe.msgprint("Printing failed: " + error);
                console.error(error);
            }
        });
    
        frm.set_df_property("naming_series", "hidden", 1);
        frm.set_df_property("company", "hidden", 1);

        // frm.set_query("practitioner", function () {
        //     return {
        //         query: "opterp_health.opterp_health.patient_appointment.get_healthcare_practitioners",
        //         filters: {
        //             role: "Physician",
        //         },
        //     };
        // });
        if (frm.doc.patient) {
            frm.set_query("insurance_policy", function () {
                return {
                    filters: {
                        patient: frm.doc.patient,
                    },
                };
            });
        }
        if (
            ["Open", "Checked In", "Confirmed"].includes(frm.doc.status) ||
            (frm.doc.status == "Scheduled" && !frm.doc.__islocal)
        ) {
            frm.add_custom_button(__("Cancel"), function () {
                update_status(frm, "Cancelled");
            });
            frm.add_custom_button(__("Reschedule"), function () {
                custom_check_and_set_availability(frm);
            });

            if (frm.doc.procedure_template) {
                frm.add_custom_button(
                    __("Clinical Procedure"),
                    function () {
                        frappe.model.open_mapped_doc({
                            method: "healthcare.healthcare.doctype.clinical_procedure.clinical_procedure.make_procedure",
                            frm: frm,
                        });
                    },
                    __("Create")
                );
            } else if (frm.doc.therapy_type) {
                frm.add_custom_button(
                    __("Therapy Session"),
                    function () {
                        frappe.model.open_mapped_doc({
                            method: "healthcare.healthcare.doctype.therapy_session.therapy_session.create_therapy_session",
                            frm: frm,
                        });
                    },
                    "Create"
                );
            } else {
                frm.add_custom_button(
                    __("Patient Encounter"),
                    function () {
                        frappe.model.open_mapped_doc({
                            method: "healthcare.healthcare.doctype.patient_appointment.patient_appointment.make_encounter",
                            frm: frm,
                        });
                    },
                    __("Create")
                );
            }

            frm.add_custom_button(
                __("Vital Signs"),
                function () {
                    create_vital_signs(frm);
                },
                __("Create")
            );

            if (["Open", "Scheduled"].includes(frm.doc.status)) {
                frm.add_custom_button(
                    __("Confirm"),
                    function () {
                        frm.set_value("status", "Confirmed");
                        frm.save();
                    },
                    __("Status")
                );
            }
        }
    },
    set_check_availability_action: function (frm) {
        frm.page.set_primary_action(__("Check Availability"), function () {
            if (!frm.doc.patient) {
                frappe.msgprint({
                    title: __("Not Allowed"),
                    message: __("Please select Patient first"),
                    indicator: "red",
                });
            } else {
                custom_check_and_set_availability(frm);
            }
        });
    },
});

let custom_check_and_set_availability = function (frm) {
    let selected_slot = null;
    let service_unit = null;
    let duration = null;
    let add_video_conferencing = null;
    let overlap_appointments = null;
    let appointment_based_on_check_in = false;

    show_availability();

    function show_empty_state(practitioner, appointment_date) {
        frappe.msgprint({
            title: __("Not Available"),
            message: __("Healthcare Practitioner {0} not available on {1}", [
                practitioner.bold(),
                appointment_date.bold(),
            ]),
            indicator: "red",
        });
    }

    function show_availability() {
        let d = new frappe.ui.Dialog({
            title: __("Available slots"),
            fields: [
                {
                    fieldtype: "Link",
                    options: "Medical Department",
                    fieldname: "department",
                    label: "Medical Department",
                },
                { fieldtype: "Column Break" },
                {
                    fieldtype: "Link",
                    options: "Healthcare Practitioner",
                    reqd: 1,
                    fieldname: "practitioner",
                    label: "Healthcare Practitioner",
                },
                { fieldtype: "Column Break" },
                {
                    fieldtype: "Date",
                    reqd: 1,
                    fieldname: "appointment_date",
                    label: "Date",
                    min_date: new Date(frappe.datetime.get_today()),
                },
                { fieldtype: "Section Break" },
                { fieldtype: "HTML", fieldname: "available_slots" },
            ],
            primary_action_label: __("Book"),
            primary_action: async function () {
                frm.set_value("appointment_time", selected_slot);
                add_video_conferencing =
                    add_video_conferencing &&
                    !d.$wrapper.find(".opt-out-check").is(":checked") &&
                    !overlap_appointments;

                frm.set_value("add_video_conferencing", add_video_conferencing);
                if (!frm.doc.duration) {
                    frm.set_value("duration", duration);
                }

                let practitioner = frm.doc.practitioner;

                frm.set_value("practitioner", d.get_value("practitioner"));
                frm.set_value("department", d.get_value("department"));
                frm.set_value("appointment_date", d.get_value("appointment_date"));
                frm.set_value("appointment_based_on_check_in", appointment_based_on_check_in);

                if (service_unit) {
                    frm.set_value("service_unit", service_unit);
                }

                d.hide();
                frm.enable_save();
                await frm.save();

                if (
                    !frm.is_new() &&
                    (!practitioner || practitioner === d.get_value("practitioner"))
                ) {
                    await frappe.db
                        .get_single_value("Healthcare Settings", "show_payment_popup")
                        .then((val) => {
                            frappe.call({
                                method: "healthcare.healthcare.doctype.fee_validity.fee_validity.check_fee_validity",
                                args: { appointment: frm.doc },
                                callback: (r) => {
                                    if (val && !r.message && !frm.doc.invoiced) {
                                        make_payment(frm, val);
                                    } else {
                                        frappe.call({
                                            method: "healthcare.healthcare.doctype.patient_appointment.patient_appointment.update_fee_validity",
                                            args: { appointment: frm.doc },
                                        });
                                    }
                                },
                            });
                        });
                }

                d.get_primary_btn().attr("disabled", true);
            },
        });

        // Set initial values
        d.set_values({
            department: frm.doc.department,
            practitioner: frm.doc.practitioner,
            appointment_date: frm.doc.appointment_date,
        });

        // disable dialog action initially
        d.get_primary_btn().attr("disabled", true);

        let fd = d.fields_dict;
        let department_field = fd["department"];
        let practitioner_field = fd["practitioner"];
        let appointment_date_field = fd["appointment_date"];
        let updating_department_from_practitioner = false;
        let previous_department = frm.doc.department || "";
        function set_practitioner_filter(dept) {
            practitioner_field.get_query = function () {
                return {
                    filters: dept ? { department: dept } : {},
                };
            };
        }

        // Department change handler
        department_field.df.onchange = () => {
            let current_dept = d.get_value("department");

            if (!updating_department_from_practitioner) {
                if (!current_dept || current_dept !== previous_department) {
                    d.set_value("practitioner", "");
                }
            }

            previous_department = current_dept;
            set_practitioner_filter(current_dept);
        };

        // Practitioner change handler
        practitioner_field.df.onchange = () => {
            let selected_pract = d.get_value("practitioner");

            if (selected_pract) {
                frappe.db
                    .get_value("Healthcare Practitioner", selected_pract, "department")
                    .then((r) => {
                        if (r.message && r.message.department) {
                            updating_department_from_practitioner = true;
                            d.set_value("department", r.message.department);
                            setTimeout(() => {
                                updating_department_from_practitioner = false;
                            }, 100);
                        }
                        show_slots(d, fd);
                    });
            } else {
                set_practitioner_filter(d.get_value("department"));
            }
        };

        // Appointment date change handler
        appointment_date_field.df.onchange = () => {
            show_slots(d, fd);
        };

        d.show();
    }

    function show_slots(d, fd) {
        if (d.get_value("appointment_date") && d.get_value("practitioner")) {
            fd.available_slots.html("");
            frappe.call({
                method: "healthcare.healthcare.doctype.patient_appointment.patient_appointment.get_availability_data",
                args: {
                    practitioner: d.get_value("practitioner"),
                    date: d.get_value("appointment_date"),
                    appointment: frm.doc,
                },
                callback: (r) => {
                    let data = r.message;
                    if (data.slot_details.length > 0) {
                        let $wrapper = d.fields_dict.available_slots.$wrapper;

                        // make buttons for each slot
                        let slot_html = get_slots(
                            data.slot_details,
                            data.fee_validity,
                            d.get_value("appointment_date")
                        );

                        $wrapper.css("margin-bottom", 0).addClass("text-center").html(slot_html);

                        // highlight button when clicked
                        $wrapper.on("click", "button", function () {
                            let $btn = $(this);
                            $wrapper.find("button").removeClass("btn-outline-primary");
                            $btn.addClass("btn-outline-primary");
                            selected_slot = $btn.attr("data-name");
                            service_unit = $btn.attr("data-service-unit");
                            appointment_based_on_check_in = $btn.attr("data-day-appointment");
                            duration = $btn.attr("data-duration");
                            add_video_conferencing = parseInt($btn.attr("data-tele-conf"));
                            overlap_appointments = parseInt($btn.attr("data-overlap-appointments"));
                            // show option to opt out of tele conferencing
                            if ($btn.attr("data-tele-conf") == 1) {
                                if (d.$wrapper.find(".opt-out-conf-div").length) {
                                    d.$wrapper.find(".opt-out-conf-div").show();
                                } else {
                                    overlap_appointments
                                        ? d.footer.prepend(
                                              `<div class="opt-out-conf-div ellipsis text-muted" style="vertical-align:text-bottom;">
												<label>
													<span class="label-area">
													${__("Video Conferencing disabled for group consultations")}
													</span>
												</label>
											</div>`
                                          )
                                        : d.footer.prepend(
                                              `<div class="opt-out-conf-div ellipsis" style="vertical-align:text-bottom;">
											<label>
												<input type="checkbox" class="opt-out-check"/>
												<span class="label-area">
												${__("Do not add Video Conferencing")}
												</span>
											</label>
										</div>`
                                          );
                                }
                            } else {
                                d.$wrapper.find(".opt-out-conf-div").hide();
                            }

                            // enable primary action 'Book'
                            d.get_primary_btn().attr("disabled", null);
                        });
                    } else {
                        //	fd.available_slots.html('Please select a valid date.'.bold())
                        show_empty_state(
                            d.get_value("practitioner"),
                            d.get_value("appointment_date")
                        );
                    }
                },
                freeze: true,
                freeze_message: __("Fetching Schedule..."),
            });
        } else {
            fd.available_slots.html(
                __("Appointment date and Healthcare Practitioner are Mandatory").bold()
            );
        }
    }

    function get_slots(slot_details, fee_validity, appointment_date) {
        let slot_html = "";
        let appointment_count = 0;
        let disabled = false;
        let start_str,
            slot_start_time,
            slot_end_time,
            interval,
            count,
            count_class,
            tool_tip,
            available_slots;

        slot_details.forEach((slot_info) => {
            slot_html += `<div class="slot-info">`;
            if (fee_validity && fee_validity != "Disabled") {
                slot_html += `
					<span style="color:green">
					${__("Patient has fee validity till")} <b>${moment(fee_validity.valid_till).format(
                    "DD-MM-YYYY"
                )}</b>
					</span><br>`;
            } else if (fee_validity != "Disabled") {
                slot_html += `
					<span style="color:red">
					${__("Patient has no fee validity")}
					</span><br>`;
            }

            slot_html += `
				<span><b>
				${__("Practitioner Schedule: ")} </b> ${slot_info.slot_name}
					${
                        slot_info.tele_conf && !slot_info.allow_overlap
                            ? '<i class="fa fa-video-camera fa-1x" aria-hidden="true"></i>'
                            : ""
                    }
				</span><br>
				<span><b> ${__("Service Unit: ")} </b> ${slot_info.service_unit}</span>`;
            if (slot_info.service_unit_capacity) {
                slot_html += `<br><span> <b> ${__("Maximum Capacity:")} </b> ${
                    slot_info.service_unit_capacity
                } </span>`;
            }

            slot_html += "</div><br>";

            slot_html += slot_info.avail_slot
                .map((slot) => {
                    appointment_count = 0;
                    disabled = false;
                    count_class = tool_tip = "";
                    start_str = slot.from_time;
                    slot_start_time = moment(slot.from_time, "HH:mm:ss");
                    slot_end_time = moment(slot.to_time, "HH:mm:ss");
                    interval = ((slot_end_time - slot_start_time) / 60000) | 0;

                    // restrict past slots based on the current time.
                    let now = moment();
                    let booked_moment = "";
                    if (
                        now.format("YYYY-MM-DD") == appointment_date &&
                        slot_start_time.isBefore(now) &&
                        !slot.maximum_appointments
                    ) {
                        disabled = true;
                    } else {
                        // iterate in all booked appointments, update the start time and duration
                        slot_info.appointments.forEach((booked) => {
                            booked_moment = moment(booked.appointment_time, "HH:mm:ss");
                            let end_time = booked_moment.clone().add(booked.duration, "minutes");

                            // to get apointment count for all day appointments
                            if (slot.maximum_appointments) {
                                if (booked.appointment_date == appointment_date) {
                                    appointment_count++;
                                }
                            }
                            // Deal with 0 duration appointments
                            if (
                                booked_moment.isSame(slot_start_time) ||
                                booked_moment.isBetween(slot_start_time, slot_end_time)
                            ) {
                                if (booked.duration == 0) {
                                    disabled = true;
                                    return false;
                                }
                            }

                            // Check for overlaps considering appointment duration
                            if (slot_info.allow_overlap != 1) {
                                if (
                                    slot_start_time.isBefore(end_time) &&
                                    slot_end_time.isAfter(booked_moment)
                                ) {
                                    // There is an overlap
                                    disabled = true;
                                    return false;
                                }
                            } else {
                                if (
                                    slot_start_time.isBefore(end_time) &&
                                    slot_end_time.isAfter(booked_moment)
                                ) {
                                    appointment_count++;
                                }
                                if (appointment_count >= slot_info.service_unit_capacity) {
                                    // There is an overlap
                                    disabled = true;
                                    return false;
                                }
                            }
                        });
                    }
                    if (slot_info.allow_overlap == 1 && slot_info.service_unit_capacity > 1) {
                        available_slots = slot_info.service_unit_capacity - appointment_count;
                        count = `${available_slots > 0 ? available_slots : __("Full")}`;
                        count_class = `${available_slots > 0 ? "badge-success" : "badge-danger"}`;
                        tool_tip = `${available_slots} ${__("slots available for booking")}`;
                    }

                    if (slot.maximum_appointments) {
                        if (appointment_count >= slot.maximum_appointments) {
                            disabled = true;
                        } else {
                            disabled = false;
                        }
                        available_slots = slot.maximum_appointments - appointment_count;
                        count = `${available_slots > 0 ? available_slots : __("Full")}`;
                        count_class = `${available_slots > 0 ? "badge-success" : "badge-danger"}`;
                        return `<button class="btn btn-secondary" data-name=${start_str}
								data-service-unit="${slot_info.service_unit || ""}"
								data-day-appointment=${1}
								data-duration=${slot.duration}
								${disabled ? 'disabled="disabled"' : ""}>${slot.from_time} -
								${slot.to_time} ${
                            slot.maximum_appointments
                                ? `<br><span class='badge ${count_class}'>${count} </span>`
                                : ""
                        }</button>`;
                    } else {
                        return `
							<button class="btn btn-secondary" data-name=${start_str}
								data-duration=${interval}
								data-service-unit="${slot_info.service_unit || ""}"
								data-tele-conf="${slot_info.tele_conf || 0}"
								data-overlap-appointments="${slot_info.service_unit_capacity || 0}"
								style="margin: 0 10px 10px 0; width: auto;" ${disabled ? 'disabled="disabled"' : ""}
								data-toggle="tooltip" title="${tool_tip || ""}">
								${start_str.substring(0, start_str.length - 3)}
								${slot_info.service_unit_capacity ? `<br><span class='badge ${count_class}'> ${count} </span>` : ""}
							</button>`;
                    }
                })
                .join("");

            if (slot_info.service_unit_capacity) {
                slot_html += `<br/><small>${__(
                    "Each slot indicates the capacity currently available for booking"
                )}</small>`;
            }
            slot_html += `<br/><br/>`;
        });

        return slot_html;
    }
};