frappe.ui.form.on("Quick Patient Appointment", {
    onload: function(frm){
        frm.set_value("mobile_no","+977-");
        if (window.workstationInstance && window.workstationInstance.page_name === "health-counter") {
            if (window.workstationInstance.selectedPatient) {
                frm.set_value("patient", window.workstationInstance.selectedPatient);
            }
        }
    },
    refresh: function (frm) {
        frm.disable_save();

        frm.page.wrapper.find(".menu-btn-group").hide();

        frm.scroll_to_field("patient_name");

        frm.set_value("date",frappe.datetime.now_date());
        frm.set_df_property("appointment_type", "hidden", true);
        frm.set_df_property("appointment_for", "hidden", true);
        frm.set_value("appointment_type", "Specialist");

        function update_button_label() {
            frm.clear_custom_buttons();
    
            const label = frm.doc.patient ? "Create Appointment" : "Create Patient & Appointment";
    
            frm.add_custom_button(label, function () {
                validate_mobile(frm);
            
                frappe.call({
                    method: "opterp_health.opterp_health.doctype.quick_patient_appointment.quick_patient_appointment.quick_admit",
                    args: {
                        data: JSON.stringify(frm.doc)
                    },
                    callback: function (r) {
                        if (r.message) {
                            const { patient, appointment } = r.message;
                            frm.dashboard.set_headline(__('Booking Confirmed'));

                            if (window.workstationInstance) {
                                ["Patient", "Patient Appointment"].forEach((doctype) => {
                                    frappe.db.get_value("Print Format", { "doc_type": doctype }, "name").then((res) => {
                                        let defaultFormat = res.message ? res.message.name : 'Standard'; 
                                        let docname = (doctype === "Patient") ? patient : appointment;
            
                                        let print_url = frappe.urllib.get_full_url(
                                            `/printview?doctype=${encodeURIComponent(doctype)}&name=${encodeURIComponent(docname)}&format=${encodeURIComponent(defaultFormat)}&no_letterhead=0`
                                        );
                                        frm.set_value("patient", patient);
                                        window.open(print_url, "_blank");
                                    });
                                });
                            }
                        }
                        else{
                            frm.dashboard.set_headline(__('Booking Failed'));
                        }
                    }
                });
        }).addClass("btn-primary");
    }
        update_button_label();
        frm.fields_dict.patient.df.onchange = update_button_label;
    },
    patient: function(frm) {
        if (window.workstationInstance) {
            const targetPages = ["health-counter"];
            if (targetPages.includes(window.workstationInstance.page_name)) {
                window.workstationInstance.setSelectedPatient(frm.doc.patient);
            }
        }
        if (frm.doc.patient) {
            frappe.db.get_doc("Patient", frm.doc.patient).then(doc => {
                frm.set_value("patient_name", doc.patient_name || `${doc.first_name} ${doc.last_name || ''}`.trim());
                frm.set_value("sex", doc.sex);
                frm.set_value("mobile_no", doc.mobile);

                if (doc.dob) {
                    frm.set_df_property("age", "hidden", false);
                    frm.set_df_property("dob", doc.dob);
                    frm.set_value("age", get_age(doc.dob));
                    frm.set_df_property("age", "description", doc.dob);
                }
                else {
                    frm.set_df_property("age", "hidden", true);
                    frm.set_df_property("dob", "hidden", true);
                }
    
                frm.set_df_property("patient_name", "read_only", true);
                frm.set_df_property("sex", "read_only", true);
                frm.set_df_property("mobile_no", "read_only", true);
                frm.set_df_property("dob", "read_only", true);
                frm.set_df_property("age", "read_only", true);

            });
        } else {
            frm.set_value("patient_name", "");
            frm.set_value("sex", "");
            frm.set_value("mobile_no", "+977-");
            frm.set_value("age", "");
    
            frm.set_df_property("patient_name", "read_only", false);
            frm.set_df_property("sex", "read_only", false);
            frm.set_df_property("mobile_no", "read_only", false);
            frm.set_df_property("age", "read_only", false);
            frm.set_df_property("age", "description", "");


        }
    },
    dob: function(frm) {
        if (frm.doc.dob && !frm.doc.patient) {
            const age = get_age(frm.doc.dob);
            frm.set_value("age", age);
            frm.set_df_property("age", "description", frm.doc.dob);
        }
    },
    age: function(frm) {
        if (frm.doc.age && !frm.doc.patient) {
            const dob = get_dob_from_age(frm.doc.age);
            frm.set_value("dob", dob);
        }
    },
    healthcare_practitioner: function(frm) {
        const wrapper = frm.fields_dict.practitioner_schedule.$wrapper;
        const practitioner = frm.doc.healthcare_practitioner;
        const appointmentDate = frm.doc.date ;
        
        if (frm.doc.healthcare_practitioner=='') {
            wrapper.empty();
            frm.set_value("department", "");
            frm.set_df_property("department", "read_only", 0);
            return;
        }
        
        frappe.db.get_value("Healthcare Practitioner", practitioner, "department")
            .then(r => {
                if (r.message && r.message.department) {
                    frm.set_value("department", r.message.department);
                    frm.set_df_property("department", "read_only", 1);
                }
            });

        wrapper.html(`<div style="display: flex; justify-content: center; align-items: center; height: 200px;"><p>Loading schedule...</p></div>`);
    
        frappe.call({
            method: "opterp_health.methods.patient_appointment.check_availability",
            args: {
                practitioner: practitioner,
                date: appointmentDate
            },
            callback: function(r) {
                const response = r.message;
                wrapper.empty();
    
                if (response && response.length > 0) {
                    response.forEach(function(slotGroup) {
                        const groupSection = $(`
                            <div class="slot-group py-3">
                                <h5 class="text-center">Schedule: ${slotGroup.slot_name}</h5>
                                <div class="row g-2 justify-content-center"></div>
                            </div>
                        `).appendTo(wrapper);
    
                        const rowContainer = groupSection.find(".row");
    
                        const appointments = slotGroup.appointments.map(a =>
                            (a.appointment_time || "").slice(0, 5)
                        );
                        
                        slotGroup.avail_slot.forEach(function(slot) {
                        let fromTime = (slot.from_time || "").slice(0, 5);

                        const [hourStr, minuteStr] = fromTime.split(":");
                        const paddedFromTime = `${hourStr.padStart(2, "0")}:${minuteStr}`;

                        const isBooked = appointments.includes(fromTime);
                        const now = new Date();
                        const selectedDateTime = new Date(`${frm.doc.date}T${paddedFromTime}:00`);

                        const isPast = selectedDateTime < now;
                        const disabled = isBooked || isPast;

                        if (fromTime.endsWith(":")) {
                            fromTime = fromTime.slice(0, -1);
                        }

                        const slotBtn = $(`

                            <style>
                                .custom-btn {
                                    background-color: #28a745; /* Original green */
                                    color: white;
                                    border: none;
                                    transition: 0.3s;
                                }
                                .custom-btn:hover {
                                    background-color: #218838; /* Darker green on hover */
                                }
                                .custom-btn:disabled {
                                    background-color: #D3D3D3; /* Gray when disabled */
                                }
                                .custom-btn.selected {
                                    background-color: #007bff; /* Blue when selected */
                                }
                                .custom-btn.selected:hover {
                                    background-color: #0056b3; /* Darker blue on hover for selected */
                                }
                            </style>
                                <div class="col-md-2 col-sm-3 col-4 d-flex justify-content-center">
                                    <button class="custom-btn rounded-pill px-3 py-2 m-1"
                                            style="font-size: 12px; min-width: 80px;" ${disabled ? 'disabled' : ''} >
                                        ${fromTime}
                                    </button>
                                </div>
                            `);

                            if (!isBooked) {
                                slotBtn.find("button").on("click", function(e) {
                                    e.preventDefault();
                                    e.stopPropagation();

                                    // Remove 'selected' class from any currently selected button
                                    $('.custom-btn.selected').removeClass('selected');

                                    // Add 'selected' class to the clicked button
                                    $(this).addClass('selected');

                                    frm.set_value("appointment_time", fromTime);
                                    frm.set_value("service_unit", slotGroup.service_unit);
                                });
                            }
                        
                            rowContainer.append(slotBtn);
                        });
                        
                    });
                } else {
                    wrapper.html('<p class="text-muted text-center">No available slots for the selected practitioner and date.</p>');
                }
            },
            error: function() {
                wrapper.html(`<p class="text-danger">Error loading availability. Try again later.</p>`);
            }
        });
    },
    date: function(frm) {
         frm.set_value("appointment_time", "");
         frm.set_value("service_unit", "");
        if (frm.doc.healthcare_practitioner) {
            frm.trigger("healthcare_practitioner");
        }
    },    
    appointment_type: function (frm) {
        if (frm.doc.appointment_type === 'General') {
            frm.set_value('appointment_for', 'Department');
        } else if (frm.doc.appointment_type === 'Specialist') {
            frm.set_value('appointment_for', 'Practitioner');
            // frm.set_df_property('department', 'hidden', true);

        }
    },
    appointment_time:function(frm){
        if (frm.doc.appointment_time==""){
            frm.set_df_property('appointment_time', 'hidden', true);
            frm.set_df_property('service_unit', 'hidden', true);
        }
        else{
            frm.set_df_property('appointment_time', 'hidden', false);
            frm.set_df_property('service_unit', 'hidden', false);
        }
    },
    department: function (frm) {
		if (frm.doc.department) {
			frm.set_query('healthcare_practitioner', function () {
                return {
                    filters: {
                        department: frm.doc.department
                    }
                };
            });
		}
	},
    mobile_no: function(frm) {
        if (frm.doc.mobile_no && frm.doc.mobile_no.length >= 15) {
            const isValid = validate_mobile(frm);

            if (isValid) {
                frm.set_df_property("mobile_no", "description", 
                    '<span style="color:green;">Valid phone number format.</span>');
            } else {
                if(frm.doc.mobile_no.startsWith("+977-")) {
                    frm.set_df_property("mobile_no", "description", 
                        '<span style="color:red;">Please enter a valid 10-digit Nepali mobile number in the format +977-98XXXXXXXX.</span>');
                }
            }
        } else {
            frm.set_df_property("mobile_no", "description", 
                '');
        }
    },
    validate: function(frm) {
        frappe.validated = true;
    }
});

function validate_mobile(frm) {
    const mobile = frm.doc.mobile_no;

    if (mobile && mobile.length > 0) {
        const parts = mobile.split('-');

        if (parts.length === 2) {
            const countryCode = parts[0];
            const number = parts[1];

            if (countryCode === "+977") {
                const nepalMobileRegex = /^(9)\d{9}$/;
                return nepalMobileRegex.test(number);
            }
        }
    }

    return false;
}

function get_age(dob_str) {
    if (!dob_str) return "";

    const dob = frappe.datetime.str_to_obj(dob_str);
    const today = frappe.datetime.str_to_obj(frappe.datetime.now_date());

    let age = today.getFullYear() - dob.getFullYear();
    const monthDiff = today.getMonth() - dob.getMonth();
    const dayDiff = today.getDate() - dob.getDate();

    if (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)) {
        age -= 1;
    }

    return `${age}`;
}

function get_dob_from_age(age) {
    const today = new Date();
    const birthYear = today.getFullYear() - parseInt(age);
    return frappe.datetime.obj_to_str(new Date(birthYear, today.getMonth(), today.getDate()));
}
