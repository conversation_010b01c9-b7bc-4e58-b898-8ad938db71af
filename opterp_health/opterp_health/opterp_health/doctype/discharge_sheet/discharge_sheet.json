{"actions": [], "allow_rename": 1, "creation": "2025-07-31 14:19:40.810847", "doctype": "DocType", "engine": "InnoDB", "field_order": ["patient", "inpatient_record", "discharge_type", "column_break_mrub", "date_of_admission", "date_of_discharge", "primary_practitioner", "section_break_uzbq", "final_diagnosis", "section_break_uipw", "chief_complaint", "personal_history", "drug_and_allergy", "medication_during_hospital_stay", "column_break_jwef", "history_of_present_<PERSON><PERSON><PERSON>", "general_examination", "systemic_examination", "medication_during_discharge", "section_break_vphk", "relevant_investigations", "follow_up", "amended_from", "signatures_section", "signatories"], "fields": [{"fieldname": "chief_complaint", "fieldtype": "Long Text", "label": "Chief <PERSON><PERSON><PERSON><PERSON>"}, {"fieldname": "history_of_present_<PERSON><PERSON><PERSON>", "fieldtype": "Long Text", "label": "History of Present Illness(HOPI)"}, {"fieldname": "personal_history", "fieldtype": "Long Text", "label": "Personal History"}, {"fieldname": "drug_and_allergy", "fieldtype": "Long Text", "label": "Drug and Allergy"}, {"fieldname": "general_examination", "fieldtype": "Long Text", "label": "General Examination"}, {"fieldname": "systemic_examination", "fieldtype": "Long Text", "label": "Systemic Examination"}, {"fieldname": "medication_during_hospital_stay", "fieldtype": "Long Text", "label": " Medication During Hospital Stay "}, {"fieldname": "medication_during_discharge", "fieldtype": "Long Text", "label": " Medication During Discharge "}, {"fieldname": "follow_up", "fieldtype": "Small Text", "label": " Follow Up "}, {"fieldname": "column_break_jwef", "fieldtype": "Column Break"}, {"fieldname": "discharge_type", "fieldtype": "Select", "in_list_view": 1, "label": "Discharge Type", "options": "Discharge\nLAMA\nDOPR\nRefer"}, {"fieldname": "patient", "fieldtype": "Link", "in_filter": 1, "in_list_view": 1, "in_standard_filter": 1, "label": "Patient", "options": "Patient"}, {"fieldname": "inpatient_record", "fieldtype": "Link", "label": "Inpatient Record", "options": "Inpatient Record", "unique": 1}, {"fieldname": "section_break_uipw", "fieldtype": "Section Break"}, {"fieldname": "column_break_mrub", "fieldtype": "Column Break"}, {"fieldname": "date_of_admission", "fieldtype": "Date", "in_filter": 1, "in_list_view": 1, "in_standard_filter": 1, "label": "Date of Admission"}, {"fieldname": "date_of_discharge", "fieldtype": "Date", "in_list_view": 1, "in_standard_filter": 1, "label": "Date of Discharge"}, {"fieldname": "primary_practitioner", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Primary Practitioner", "options": "Healthcare Practitioner"}, {"fieldname": "section_break_uzbq", "fieldtype": "Section Break"}, {"fieldname": "final_diagnosis", "fieldtype": "Text Editor", "label": "Final Diagnosis"}, {"fieldname": "section_break_vphk", "fieldtype": "Section Break"}, {"fieldname": "relevant_investigations", "fieldtype": "Text Editor", "label": "Relevant Investigations"}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "Discharge Sheet", "print_hide": 1, "read_only": 1, "search_index": 1}, {"fieldname": "signatures_section", "fieldtype": "Section Break", "label": "Signatures"}, {"fieldname": "signatories", "fieldtype": "Table", "label": "Signatories", "options": "Discharge Signatories"}], "hide_toolbar": 1, "index_web_pages_for_search": 1, "is_submittable": 1, "links": [], "modified": "2025-08-03 07:06:06.761426", "modified_by": "Administrator", "module": "Opterp Health", "name": "Discharge Sheet", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Physician", "share": 1, "submit": 1, "write": 1}, {"create": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Nursing User", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": []}