// Copyright (c) 2025, <PERSON><PERSON><PERSON> and contributors
// For license information, please see license.txt

frappe.ui.form.on("Emergency", {
	onload(frm) {
		if (frm.is_new()) {
			frm.set_value("arrival_time", frappe.datetime.now_datetime());
		}

		if (frm.doc.patient) {
			set_patient_details(frm, frm.doc.patient);
		}
	},
	patient(frm) {
		if (frm.doc.patient) {
			set_patient_details(frm, frm.doc.patient);
		}
	},
	refresh(frm) {
    	if (!frm.is_new()) {
			if (!frm.doc.vitals && frm.doc.docstatus === 0){
				frm.add_custom_button("Take Vitals", () => {
					window.workstationInstance.renderNewForm("Vital Signs", "#content-section", {
						doc_vals: {
							patient: frm.doc.patient,
							emergency: frm.doc.name
						}	
					});
				},__("Create"));
			}

			if (frm.doc.status != "Discharged") {
				frm.add_custom_button("Make Encounter", () => {
						window.workstationInstance.renderNewForm("Patient Encounter", "#content-section", {
							doc_vals: {
								patient: frm.doc.patient,
								emergency: frm.doc.name
							}	
						});
					},
					__("Create"));
			}

			const dischargeable_status = ["Open","Under Treatment"];

			if (dischargeable_status.includes(frm.doc.status) && !frm.doc.inpatient_record) {
				frm.add_custom_button("Discharge", () => {
					frappe.call({
						doc: frm.doc,
						method: "discharge",
						callback: function() {
							frappe.msgprint("Discharged successfully");
							frm.reload_doc();
						}
					});
				});
			}

			if (!frm.doc.inpatient_record && frm.doc.status != "Discharged") {
				frm.add_custom_button("Admit", () => {
					window.workstationInstance.renderNewForm(
						"Inpatient Record",
						"#content-section",
						{
							doc_vals: {
								patient: frm.doc.patient,
								emergency: frm.doc.name,
								admitted_datetime: frappe.datetime.now_datetime()
							},
						}
					).then(() => {
						const form = window.workstationInstance.currentForm;
						form.set_df_property("emergency", "read_only", 1);
						form.set_df_property("patient", "read_only", 1);
						form.set_df_property("admitted_datetime", "read_only", 1);
						form.scroll_to_field("medical_department");
					});
				});
			}
        }
	},

	create_patient: function(frm){
		frappe.call({
			method: "opterp_health.opterp_health.doctype.emergency.emergency.create_patient",
			args: {
				data: JSON.stringify(frm.doc)
			},
			callback: function (r) {
				if (r.message) {
					const { patient} = r.message;
					if (patient){
						frm.set_value("patient",patient);
					}
				}
			}
		});
		
	}
});

function set_patient_details(frm, patient_id) {
	frappe.db.get_value("Patient", patient_id, ["patient_name", "sex", "dob","mobile"])
		.then(({ message }) => {
			if (message) {
				const { patient_name, sex, dob, mobile } = message;

				if (patient_name) frm.set_value("patient_name", patient_name);
				if (sex) frm.set_value("sex", sex);
				if (dob) {
					const age = calculate_age(dob);
					frm.set_value("age", age);
				}
				if (mobile) frm.set_value("contact", mobile);

				frm.set_df_property("patient_name", "read_only", 1);
				frm.set_df_property("sex", "read_only", 1);
				frm.set_df_property("age", "read_only", 1);
				frm.set_df_property("contact", "read_only", 1);
			}
		});
}

function calculate_age(dob) {
	const birthDate = new Date(dob);
	const today = new Date();

	let age = today.getFullYear() - birthDate.getFullYear();
	const m = today.getMonth() - birthDate.getMonth();

	if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
		age--;
	}

	return age;
}
