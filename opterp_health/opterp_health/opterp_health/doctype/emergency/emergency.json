{"actions": [], "allow_rename": 1, "autoname": "format: EMR-{patient}-{####}", "creation": "2025-06-05 15:12:35.277439", "doctype": "DocType", "engine": "InnoDB", "field_order": ["patient", "patient_name", "sex", "age", "contact", "create_patient", "brought_by", "column_break_edsq", "status", "arrival_time", "practitioner", "triage", "mode_of_arrival", "section_break_sfvw", "vitals", "refer_to_department", "column_break_tuor", "chief_complaint", "inpatient_record", "section_break_xybg", "remark_column", "address", "column_break_wjdg", "small_text_alqv", "amended_from"], "fields": [{"fieldname": "patient", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Patient", "options": "Patient"}, {"fieldname": "patient_name", "fieldtype": "Data", "in_list_view": 1, "label": "Patient Name"}, {"fieldname": "sex", "fieldtype": "Link", "label": "Sex", "options": "Gender"}, {"fieldname": "age", "fieldtype": "Int", "label": "Age", "non_negative": 1}, {"fieldname": "column_break_edsq", "fieldtype": "Column Break"}, {"fieldname": "practitioner", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Practitioner", "options": "Healthcare Practitioner"}, {"fieldname": "triage", "fieldtype": "Select", "hidden": 1, "label": "Triage", "options": "\nASAP\nURGENT\nSTAT"}, {"fieldname": "section_break_xybg", "fieldtype": "Section Break"}, {"fieldname": "remark_column", "fieldtype": "Column Break"}, {"fieldname": "contact", "fieldtype": "Phone", "in_list_view": 1, "in_standard_filter": 1, "label": "Contact"}, {"fieldname": "address", "fieldtype": "Data", "label": "Address"}, {"fieldname": "small_text_alqv", "fieldtype": "Small Text", "label": "Remark"}, {"fieldname": "column_break_wjdg", "fieldtype": "Column Break"}, {"fieldname": "section_break_sfvw", "fieldtype": "Section Break"}, {"fieldname": "vitals", "fieldtype": "Link", "label": "<PERSON><PERSON>", "options": "Vital Signs"}, {"fieldname": "refer_to_department", "fieldtype": "Link", "label": "Refer to Department", "options": "Medical Department"}, {"fieldname": "mode_of_arrival", "fieldtype": "Select", "label": "Mode of Arrival", "options": "\nWalk-in\nAmbulance\nReferred\nPolice Case"}, {"fieldname": "column_break_tuor", "fieldtype": "Column Break"}, {"fieldname": "chief_complaint", "fieldtype": "Table MultiSelect", "label": "Chief <PERSON><PERSON><PERSON><PERSON>", "options": "Patient Encounter Symptom"}, {"allow_on_submit": 1, "fieldname": "inpatient_record", "fieldtype": "Link", "label": "Inpatient Record", "options": "Inpatient Record"}, {"fieldname": "brought_by", "fieldtype": "Data", "label": "Brought By"}, {"fieldname": "arrival_time", "fieldtype": "Datetime", "in_list_view": 1, "label": "Arrival Time"}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "Emergency", "print_hide": 1, "read_only": 1, "search_index": 1}, {"allow_on_submit": 1, "default": "Open", "fieldname": "status", "fieldtype": "Select", "label": "Status", "options": "Open\nUnder Treatment\nAdmitted\nDischarged", "read_only": 1}, {"depends_on": "eval: doc.patient_name && doc.sex && doc.contact && !doc.patient;", "fieldname": "create_patient", "fieldtype": "<PERSON><PERSON>", "label": "Create Patient"}], "index_web_pages_for_search": 1, "is_submittable": 1, "links": [], "modified": "2025-07-30 12:15:32.360838", "modified_by": "Administrator", "module": "Opterp Health", "name": "Emergency", "naming_rule": "Expression", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": []}