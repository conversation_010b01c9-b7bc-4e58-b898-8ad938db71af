# Copyright (c) 2025, <PERSON><PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document
from datetime import datetime, timedelta
from frappe import _
from datetime import date
from dateutil.relativedelta import relativedelta

def get_dob_from_age(age):
    try:
        age = int(age)
        if age < 0 or age > 125:
            raise ValueError("Age must be between 0 and 125.")
    except (ValueError, TypeError):
        frappe.throw("Invalid age. Please enter a number between 0 and 125.")

    today = date.today()
    dob = today - relativedelta(years=age)
    return dob

class Emergency(Document):
	def on_cancel(self):
		frappe.db.set_value(self.doctype, self.name, "status", "Cancelled")
		frappe.db.commit()


	def before_save(self):
		# todo merge new patient data with existing patient incase of accidental patient recreation
		if (not self.patient) and self.patient_name:
			# cannot verify patient 
			existing_patient = frappe.get_all(
				"Patient",
				filters={
					"patient_name": self.patient_name,
				},
				limit=1
			)

			if existing_patient:
				self.patient = existing_patient[0].name
			else:
				approx_dob = None
				if self.age:
					try:
						approx_dob = datetime.today().replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(days=int(self.age) * 365)
					except:
						pass

				patient = frappe.new_doc("Patient")
				patient.first_name = self.patient_name
				patient.sex = self.sex
				if approx_dob:
					patient.dob = approx_dob.date()
				patient.flags.ignore_mandatory = True  # Skip required fields
				patient.insert(ignore_permissions=True)

				self.patient = patient.name
                    
	@frappe.whitelist()
	def discharge(self):
		if self.docstatus != 1:
			frappe.throw("Document must be submitted before discharging.")

		self.status = "Discharged"
		self.save(ignore_permissions=True)
		frappe.msgprint("Patient discharged.")
                    

@frappe.whitelist()
def create_patient(data):
    try:
        data = frappe._dict(frappe.parse_json(data))
    except Exception as e:
        frappe.throw(_("Invalid data format: {0}").format(str(e)))
    
    if not data.patient:
        missing = []

        if not data.patient_name:
            missing.append("Patient Name")
        if not data.sex:
            missing.append("Sex")
        if not data.contact:
            missing.append("Contact")
        if not any([data.age,data.dob]):
            missing.append("Age/DOB")

        if missing:
            frappe.throw(_("The following fields are mandatory: {0}").format(", ".join(missing)))

        try:
            name_parts = data.patient_name.strip().split()
            first_name = name_parts[0]

            if len(name_parts) == 1:
                middle_name = ""
                last_name = "_"
            elif len(name_parts) == 2:
                middle_name = ""
                last_name = name_parts[1]
            else:
                middle_name = " ".join(name_parts[1:-1])
                last_name = name_parts[-1]

            if data.age:
                try:
                    age = int(data.age)
                except (ValueError, TypeError):
                    frappe.throw("Age must be a number.")

                if age < 0 or age > 125:
                    frappe.throw("Age must be between 0 and 125.")
                
        except Exception as e:
            frappe.throw(_("Error parsing patient name: {0}").format(str(e)))

        try:
            patient = frappe.get_doc({
                "doctype": "Patient",
                "first_name": first_name,
                "middle_name": middle_name,
                "last_name": last_name,
                "sex": data.sex,
                "mobile": data.get("contact"),
                "dob": data.dob if data.dob else get_dob_from_age(data.age)
            })
            patient.insert(ignore_permissions=True)
        except Exception as e:
            frappe.log_error(frappe.get_traceback(), "Emergency - Patient Insert Error")
            frappe.throw(_("Failed to create patient: {0}").format(str(e)))
    else:
        patient = frappe.get_doc("Patient", data.patient)

    return {"patient": patient.name}