frappe.listview_settings['Emergency'] = {
	filters: [['status', 'not in', ['Discharged']]],
	get_indicator: function (doc) {
		if (doc.status === 'Open') {
			return [__('Open'), 'red', 'status, =, Open'];
		} else if (doc.status === 'Admitted') {
			return [__('Admitted'), 'blue', 'status, =, Admitted'];
		} else if (doc.status === 'Discharged') {
			return [__('Discharged'), 'yellow', 'status, =, Discharged'];
		}  else if (doc.status === 'Under Treatment') {
			return [__('Under Treatment'), 'green', 'status, =, Under Treatment'];
		}
	}
};