import json
import frappe
from healthcare.healthcare.doctype.observation.observation import aggregate_and_return_observation_data, is_numbers_with_exceptions, set_calculated_result, Observation as OldObservation
from frappe.model.workflow import get_workflow_name, get_workflow_state_field


class Observation(OldObservation):
    def on_update(self):
        set_diagnostic_report_status(self)
        if (
            self.parent_observation
            and self.result_data
            and self.permitted_data_type in ["Quantity", "Numeric"]
        ):
            set_calculated_result(self)


@frappe.whitelist()
def get_observation_details(docname):
    reference = frappe.get_value(
        "Diagnostic Report", docname, ["docname", "ref_doctype"], as_dict=True
    )
    observation = []

    if reference.get("ref_doctype") == "Sales Invoice":
        observation = frappe.get_list(
            "Observation",
            fields=["*"],
            filters={
                "sales_invoice": reference.get("docname"),
                "parent_observation": "",
                "status": ["!=", "Cancelled"],
                "docstatus": ["!=", 2],
            },
            order_by="creation",
        )
    elif reference.get("ref_doctype") == "POS Invoice":
        observation = frappe.get_list(
            "Observation",
            fields=["*"],
            filters={
                "pos_invoice": reference.get("docname"),
                "parent_observation": "",
                "status": ["!=", "Cancelled"],
                "docstatus": ["!=", 2],
            },
            order_by="creation",
        )
    elif reference.get("ref_doctype") == "Patient Encounter":
        service_requests = frappe.get_all(
            "Service Request",
            filters={
                "source_doc": reference.get("ref_doctype"),
                "order_group": reference.get("docname"),
                "status": ["!=", "revoked-Request Status"],
                "docstatus": ["!=", 2],
            },
            order_by="creation",
            pluck="name",
        )
        observation = frappe.get_list(
            "Observation",
            fields=["*"],
            filters={
                "service_request": ["in", service_requests],
                "parent_observation": "",
                "status": ["!=", "Cancelled"],
                "docstatus": ["!=", 2],
            },
            order_by="creation",
        )

    out_data, obs_length = aggregate_and_return_observation_data(observation)

    return out_data, obs_length


def set_diagnostic_report_status(doc):
    if (
        doc.has_result()
        and doc.sales_invoice
        and not doc.has_component
        and doc.sales_invoice
    ):
        observations = frappe.db.get_all(
            "Observation",
            {
                "sales_invoice": doc.sales_invoice,
                "docstatus": 0,
                "status": ["!=", "Approved"],
                "has_component": 0,
            },
        )
        diagnostic_report = frappe.db.get_value(
            "Diagnostic Report",
            {"ref_doctype": "Sales Invoice", "docname": doc.sales_invoice},
            ["name"],
            as_dict=True,
        )
        if diagnostic_report:
            workflow_name = get_workflow_name("Diagnostic Report")
            workflow_state_field = get_workflow_state_field(workflow_name)
            if observations and len(observations) > 0:
                set_status = "Partially Approved"
            else:
                set_status = "Approved"
            set_value_dict = {"status": set_status}
            if workflow_state_field:
                set_value_dict[workflow_state_field] = set_status
            frappe.db.set_value(
                "Diagnostic Report",
                diagnostic_report.get("name"),
                set_value_dict,
                update_modified=False,
            )

    if (
        doc.has_result()
        and doc.pos_invoice
        and not doc.has_component
        and doc.pos_invoice
    ):
        observations = frappe.db.get_all(
            "Observation",
            {
                "pos_invoice": doc.pos_invoice,
                "docstatus": 0,
                "status": ["!=", "Approved"],
                "has_component": 0,
            },
        )
        diagnostic_report = frappe.db.get_value(
            "Diagnostic Report",
            {"ref_doctype": "POS Invoice", "docname": doc.pos_invoice},
            ["name"],
            as_dict=True,
        )
        if diagnostic_report:
            workflow_name = get_workflow_name("Diagnostic Report")
            workflow_state_field = get_workflow_state_field(workflow_name)
            if observations and len(observations) > 0:
                set_status = "Partially Approved"
            else:
                set_status = "Approved"
            set_value_dict = {"status": set_status}
            if workflow_state_field:
                set_value_dict[workflow_state_field] = set_status
            frappe.db.set_value(
                "Diagnostic Report",
                diagnostic_report.get("name"),
                set_value_dict,
                update_modified=False,
            )


@frappe.whitelist()
def bulk_set_observation_status(observations, status, reason=None):
    """
    Bulk approve or disapprove multiple observations

    Args:
            observations: List of observation names
            status: 'Approved' or 'Disapproved'
            reason: Reason for disapproval (required for disapproval)
    """
    if isinstance(observations, str):
        observations = frappe.parse_json(observations)

    if not observations:
        frappe.throw(_("No observations provided"))

    success_count = 0
    failed_observations = []

    for observation_name in observations:
        try:
            observation_doc = frappe.get_doc("Observation", observation_name)

            # Check if observation has result before processing
            if not observation_doc.has_result():
                failed_observations.append(
                    {"name": observation_name, "reason": "No result data available"}
                )
                continue

            # For approval, check if already approved
            if status == "Approved" and observation_doc.status == "Approved":
                continue

            # For disapproval, check if already disapproved or not approved
            if status == "Disapproved" and observation_doc.status != "Approved":
                failed_observations.append(
                    {"name": observation_name, "reason": "Observation is not approved"}
                )
                continue

            observation_doc.status = status
            if reason:
                observation_doc.disapproval_reason = reason

            if status == "Approved":
                observation_doc.submit()
            elif status == "Disapproved":
                new_doc = frappe.copy_doc(observation_doc)
                new_doc.status = ""
                new_doc.insert()
                observation_doc.cancel()

            success_count += 1

        except Exception as e:
            failed_observations.append({"name": observation_name, "reason": str(e)})

    # Prepare response message
    message = _("{0} observations processed successfully").format(success_count)
    if failed_observations:
        message += _("<br>Failed to process {0} observations").format(
            len(failed_observations)
        )

    return {
        "success_count": success_count,
        "failed_count": len(failed_observations),
        "failed_observations": failed_observations,
        "message": message,
    }


@frappe.whitelist()
def record_observation_result(values):
    values = json.loads(values)
    if values:
        values = [dict(t) for t in {tuple(d.items()) for d in values}]
        for val in values:
            if not val.get("observation"):
                return
            observation_doc = frappe.get_doc("Observation", val["observation"])
            if observation_doc.get("permitted_data_type") in [
                "Range",
                "Ratio",
                "Quantity",
                "Numeric",
            ]:
                if (
                    observation_doc.get("permitted_data_type")
                    in [
                        "Quantity",
                        "Numeric",
                    ]
                    and val.get("result")
                    and not is_numbers_with_exceptions(val.get("result"))
                ):
                    frappe.msgprint(
                        _(
                            "Non numeric result {0} is not allowed for Permitted Type {1}"
                        ).format(
                            frappe.bold(val.get("result")),
                            frappe.bold(observation_doc.get("permitted_data_type")),
                        ),
                        indicator="orange",
                        alert=True,
                    )
                    return

                if val.get("result") != observation_doc.get("result_data"):
                    # Always update result_data, even if it's empty (to clear the field)
                    observation_doc.result_data = val.get("result") or ""
                    if val.get("note"):
                        observation_doc.note = val.get("note")
                    if observation_doc.docstatus == 0:
                        observation_doc.save()
                    elif observation_doc.docstatus == 1:
                        observation_doc.save("Update")
            elif observation_doc.get("permitted_data_type") == "Text":
                if val.get("result") != observation_doc.get("result_text"):
                    # Always update result_text, even if it's empty (to clear the field)
                    observation_doc.result_text = val.get("result") or ""
                    if val.get("note"):
                        observation_doc.note = val.get("note")
                    if observation_doc.docstatus == 0:
                        observation_doc.save()
                    elif observation_doc.docstatus == 1:
                        observation_doc.save("Update")
            elif observation_doc.get("permitted_data_type") == "Select":
                if val.get("result") != observation_doc.get("result_select"):
                    # Always update result_select, even if it's empty (to clear the field)
                    observation_doc.result_select = val.get("result") or ""
                    if val.get("note"):
                        observation_doc.note = val.get("note")
                    if observation_doc.docstatus == 0:
                        observation_doc.save()
                    elif observation_doc.docstatus == 1:
                        observation_doc.save("Update")

            if observation_doc.get("observation_category") == "Imaging":
                # Always update fields, even if empty (to clear them)
                if "result" in val:
                    observation_doc.result_text = val.get("result") or ""
                if "interpretation" in val:
                    observation_doc.result_interpretation = (
                        val.get("interpretation") or ""
                    )
                if "result" in val or "interpretation" in val:
                    if val.get("note"):
                        observation_doc.note = val.get("note")
                    if observation_doc.docstatus == 0:
                        observation_doc.save()
                    elif observation_doc.docstatus == 1:
                        observation_doc.save("Update")

            if not val.get("result") and val.get("note"):
                observation_doc.note = val.get("note")
                if observation_doc.docstatus == 0:
                    observation_doc.save()
                elif observation_doc.docstatus == 1:
                    observation_doc.save("Update")
