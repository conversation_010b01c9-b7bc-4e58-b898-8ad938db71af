import healthcare.hooks
import healthcare.healthcare.utils
import healthcare.healthcare.doctype.observation.observation
from opterp_health.opterp_health.utils import validate_invoiced_on_submit,set_invoiced, manage_invoice_submit_cancel
from opterp_health.opterp_health.observation import get_observation_details
from healthcare.healthcare.doctype.healthcare_practitioner.healthcare_practitioner import (
    HealthcarePractitioner as OldHealthcarePractitioner,
)
from opterp_health.overrides.healthcare_practitioner import HealthcarePractitioner

healthcare.healthcare.utils.validate_invoiced_on_submit = (
    validate_invoiced_on_submit
)
healthcare.healthcare.utils.set_invoiced = set_invoiced
healthcare.healthcare.utils.manage_invoice_submit_cancel = manage_invoice_submit_cancel

app_title = "OptERP Health"
app_logo_url = "/assets/opterp_app/images/opterp-logo.svg"
add_to_apps_screen = [
    {
        "name": "healthcare",
        "logo": app_logo_url,
        "title": app_title,
        "route": "/app/healthcare",
        "has_permission": "erpnext.check_app_permission",
    }
]
healthcare.hooks.app_logo_url = app_logo_url
healthcare.hooks.add_to_apps_screen = add_to_apps_screen

healthcare.healthcare.doctype.observation.observation.get_observation_details = get_observation_details
OldHealthcarePractitioner.set_full_name = HealthcarePractitioner.set_full_name