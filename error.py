import frappe
from frappe.model.document import Document


@frappe.whitelist()
def sync_allow_sales_from_observation_template():
    templates = frappe.get_all(
        "Observation Template", fields=["name", "item_code", "is_billable"]
    )

    updated = []
    for template in templates:
        item_code = template.item_code
        is_billable = template.is_billable

        if not item_code:
            continue

        try:
            item = frappe.get_doc("Item", item_code)
            # Only update if value is different
            if item.is_sales_item != is_billable:
                item.is_sales_item = is_billable
                item.save()
                updated.append(item_code)
        except frappe.DoesNotExistError:
            frappe.log_error(f"Item not found: {item_code}")

    frappe.db.commit()
    return f"✔ Updated Allow Sales for {len(updated)} items: {', '.join(updated)}"


### App Versions
```
{
	"doctor_sharing": "0.0.1",
	"erpnext": "15.63.0",
	"frappe": "15.71.0",
	"healthcare": "15.1.2",
	"hrms": "15.46.0",
	"laboratory": "0.0.1",
	"opterp_app": "0.0.1",
	"opterp_health": "0.0.1",
	"pharmacy": "0.0.1",
	"sb_health": "0.0.1"
}
```
### Route
```
Form/Sales Invoice/new-sales-invoice-vuqaiblrbz
```
### Traceback
```
Traceback (most recent call last):
  File "apps/frappe/frappe/app.py", line 115, in application
    response = frappe.api.handle(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "apps/frappe/frappe/api/__init__.py", line 49, in handle
    data = endpoint(**arguments)
           ^^^^^^^^^^^^^^^^^^^^^
  File "apps/frappe/frappe/api/v1.py", line 36, in handle_rpc_call
    return frappe.handler.handle()
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "apps/frappe/frappe/handler.py", line 51, in handle
    data = execute_cmd(cmd)
           ^^^^^^^^^^^^^^^^
  File "apps/frappe/frappe/handler.py", line 84, in execute_cmd
    return frappe.call(method, **frappe.form_dict)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "apps/frappe/frappe/__init__.py", line 1751, in call
    return fn(*args, **newargs)
           ^^^^^^^^^^^^^^^^^^^^
  File "apps/frappe/frappe/utils/typing_validations.py", line 31, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
TypeError: get_payment_data() missing 1 required positional argument: 'invoice'

```
### Request Data
```
{
	"type": "POST",
	"args": {},
	"headers": {},
	"error_handlers": {},
	"url": "/api/method/erpnext.controllers.sales_and_purchase_return.get_payment_data",
	"request_id": null
}
```
### Response Data
```
{
	"exception": "TypeError: get_payment_data() missing 1 required positional argument: 'invoice'",
	"exc_type": "TypeError"
}
```

### App Versions
```
{
	"doctor_sharing": "0.0.1",
	"erpnext": "15.63.0",
	"frappe": "15.71.0",
	"healthcare": "15.1.2",
	"hrms": "15.46.0",
	"laboratory": "0.0.1",
	"opterp_app": "0.0.1",
	"opterp_health": "0.0.1",
	"pharmacy": "0.0.1",
	"sb_health": "0.0.1"
}
```
### Route
```
Form/Counter Opening Entry/new-counter-opening-entry-klaaxipfxh
```
### Traceback
```
Traceback (most recent call last):
  File "apps/frappe/frappe/app.py", line 115, in application
    response = frappe.api.handle(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "apps/frappe/frappe/api/__init__.py", line 49, in handle
    data = endpoint(**arguments)
           ^^^^^^^^^^^^^^^^^^^^^
  File "apps/frappe/frappe/api/v1.py", line 36, in handle_rpc_call
    return frappe.handler.handle()
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "apps/frappe/frappe/handler.py", line 51, in handle
    data = execute_cmd(cmd)
           ^^^^^^^^^^^^^^^^
  File "apps/frappe/frappe/handler.py", line 84, in execute_cmd
    return frappe.call(method, **frappe.form_dict)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "apps/frappe/frappe/__init__.py", line 1751, in call
    return fn(*args, **newargs)
           ^^^^^^^^^^^^^^^^^^^^
  File "apps/frappe/frappe/utils/typing_validations.py", line 31, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "apps/frappe/frappe/desk/form/save.py", line 39, in savedocs
    doc.save()
  File "apps/frappe/frappe/model/document.py", line 378, in save
    return self._save(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "apps/frappe/frappe/model/document.py", line 400, in _save
    return self.insert()
           ^^^^^^^^^^^^^
  File "apps/frappe/frappe/model/document.py", line 309, in insert
    self.run_before_save_methods()
  File "apps/frappe/frappe/model/document.py", line 1136, in run_before_save_methods
    self.run_method("validate")
  File "apps/frappe/frappe/model/document.py", line 1007, in run_method
    out = Document.hook(fn)(self, *args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "apps/frappe/frappe/model/document.py", line 1367, in composer
    return composed(self, method, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "apps/frappe/frappe/model/document.py", line 1349, in runner
    add_to_return_value(self, fn(self, *args, **kwargs))
                              ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "apps/frappe/frappe/model/document.py", line 1004, in fn
    return method_object(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "apps/opterp_app/opterp_app/opterp_app/doctype/counter_opening_entry/counter_opening_entry.py", line 12, in validate
    self.validate_duplicate_opening()
  File "apps/opterp_app/opterp_app/opterp_app/doctype/counter_opening_entry/counter_opening_entry.py", line 45, in validate_duplicate_opening
    frappe.throw(_("Cashier {0} already has an open counter entry: {1}").format(self.cashier, open_entry))
                 ^
NameError: name '_' is not defined

```
### Request Data
```
{
	"type": "POST",
	"args": {
		"doc": "{\"docstatus\":0,\"doctype\":\"Counter Opening Entry\",\"name\":\"new-counter-opening-entry-klaaxipfxh\",\"__islocal\":1,\"__unsaved\":1,\"owner\":\"Administrator\",\"naming_series\":\"COUNTER-OPEN-\",\"company\":\"S.B Health\",\"balance_details\":[{\"docstatus\":0,\"doctype\":\"Cashier Opening Balance Detail\",\"name\":\"new-cashier-opening-balance-detail-qpnhnkajkb\",\"__islocal\":1,\"__unsaved\":1,\"owner\":\"Administrator\",\"parent\":\"new-counter-opening-entry-klaaxipfxh\",\"parentfield\":\"balance_details\",\"parenttype\":\"Counter Opening Entry\",\"idx\":1,\"__unedited\":false,\"mode_of_payment\":\"Cash\",\"opening_amount\":0}],\"status\":\"Draft\",\"period_start_date\":\"2025-08-05 14:29:53\",\"posting_date\":\"2025-08-05\",\"cashier\":\"Administrator\",\"pos_profile\":\"Cash Counter 1\"}",
		"action": "Save"
	},
	"btn": {
		"jQuery37008601311713583931": {
			"events": {
				"click": [
					{
						"type": "click",
						"origType": "click",
						"guid": 700,
						"namespace": ""
					}
				]
			}
		}
	},
	"freeze": true,
	"headers": {},
	"error_handlers": {},
	"url": "/api/method/frappe.desk.form.save.savedocs",
	"request_id": null
}
```
### Response Data
```
{
	"exception": "NameError: name '_' is not defined",
	"exc_type": "NameError",
	"_exc_source": "opterp_app (app)"
}
```