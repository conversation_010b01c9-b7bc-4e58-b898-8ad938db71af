import frappe
from frappe.model.document import Document


@frappe.whitelist()
def sync_allow_sales_from_observation_template():
    templates = frappe.get_all(
        "Observation Template", fields=["name", "item_code", "is_billable"]
    )

    updated = []
    for template in templates:
        item_code = template.item_code
        is_billable = template.is_billable

        if not item_code:
            continue

        try:
            item = frappe.get_doc("Item", item_code)
            # Only update if value is different
            if item.is_sales_item != is_billable:
                item.is_sales_item = is_billable
                item.save()
                updated.append(item_code)
        except frappe.DoesNotExistError:
            frappe.log_error(f"Item not found: {item_code}")

    frappe.db.commit()
    return f"✔ Updated Allow Sales for {len(updated)} items: {', '.join(updated)}"


### App Versions
```
{
	"doctor_sharing": "0.0.1",
	"erpnext": "15.63.0",
	"frappe": "15.71.0",
	"healthcare": "15.1.2",
	"hrms": "15.46.0",
	"laboratory": "0.0.1",
	"opterp_app": "0.0.1",
	"opterp_health": "0.0.1",
	"pharmacy": "0.0.1",
	"sb_health": "0.0.1"
}
```
### Route
```
Form/Sales Invoice/new-sales-invoice-vuqaiblrbz
```
### Traceback
```
Traceback (most recent call last):
  File "apps/frappe/frappe/app.py", line 115, in application
    response = frappe.api.handle(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "apps/frappe/frappe/api/__init__.py", line 49, in handle
    data = endpoint(**arguments)
           ^^^^^^^^^^^^^^^^^^^^^
  File "apps/frappe/frappe/api/v1.py", line 36, in handle_rpc_call
    return frappe.handler.handle()
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "apps/frappe/frappe/handler.py", line 51, in handle
    data = execute_cmd(cmd)
           ^^^^^^^^^^^^^^^^
  File "apps/frappe/frappe/handler.py", line 84, in execute_cmd
    return frappe.call(method, **frappe.form_dict)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "apps/frappe/frappe/__init__.py", line 1751, in call
    return fn(*args, **newargs)
           ^^^^^^^^^^^^^^^^^^^^
  File "apps/frappe/frappe/utils/typing_validations.py", line 31, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
TypeError: get_payment_data() missing 1 required positional argument: 'invoice'

```
### Request Data
```
{
	"type": "POST",
	"args": {},
	"headers": {},
	"error_handlers": {},
	"url": "/api/method/erpnext.controllers.sales_and_purchase_return.get_payment_data",
	"request_id": null
}
```
### Response Data
```
{
	"exception": "TypeError: get_payment_data() missing 1 required positional argument: 'invoice'",
	"exc_type": "TypeError"
}
```