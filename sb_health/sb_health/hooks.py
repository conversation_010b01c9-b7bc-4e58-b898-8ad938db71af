app_name = "sb_health"
app_title = "SB Health"
app_publisher = "<PERSON>yu<PERSON>"
app_description = "SB Health"
app_email = "<EMAIL>"
app_license = "mit"

# Apps
# ------------------

# required_apps = []

# Each item in the list will be shown as an app in the apps page
# add_to_apps_screen = [
# 	{
# 		"name": "sb_health",
# 		"logo": "/assets/sb_health/logo.png",
# 		"title": "SB Health",
# 		"route": "/sb_health",
# 		"has_permission": "sb_health.api.permission.has_app_permission"
# 	}
# ]

# Includes in <head>
# ------------------

# include js, css files in header of desk.html
app_include_css = ["/assets/sb_health/css/sb_style.css", "/assets/sb_health/css/patient_page.css"]

www_include_js = [
	"/assets/sb_health/js/www/patient_appointment.js",
	"/assets/sb_health/js/www/button_actions.js",
]

# app_include_js = "/assets/sb_health/js/www/patient_appointment.js"
app_include_js = [
	"sb.bundle.js",
	"/assets/opterp_app/js/page/base_workstation.js",
	"/assets/sb_health/js/global/banner.js",
	"/assets/sb_health/js/global/workstation_list.js",
]


# include js, css files in header of web template
# web_include_css = "/assets/sb_health/css/sb_health.css"
# web_include_js = "/assets/sb_health/js/sb_health.js"

# include custom scss in every website theme (without file extension ".scss")
# website_theme_scss = "sb_health/public/scss/website"

# include js, css files in header of web form
webform_include_js = {"Patient Appointment": "public/js/patient_appointment.js"}
# webform_include_css = {"doctype": "public/css/doctype.css"}

# include js in page
page_js = {"health-counter": "public/js/utils.js", "nurse-page": "public/js/utils.js"}

# include js in doctype views
doctype_js = {
	"Patient": "public/js/patient.js",
	"Transfer Request": "public/js/transfer_request.js",
	"POS Invoice": "public/js/pos_invoice.js",
	"Sales Invoice": "public/js/sales_invoice.js",
    "Payment Reconciliation": "public/js/payment_reconciliation.js",
	"Patient Encounter": "public/js/patient_encounter.js",
	"Patient Appointment": "public/js/patient_appointment.js",
	"Payment Entry": "public/js/payment_entry.js",
	"Vital Signs": "public/js/vital_signs.js",
	"Delivery Note": "public/js/delivery_note.js",
	"Material Request": "public/js/material_request.js",
	"Inpatient Record": "public/js/inpatient_record.js",
	"Emergency": "public/js/emergency.js",
}

# doctype_list_js = {"doctype" : "public/js/doctype_list.js"}
# doctype_tree_js = {"doctype" : "public/js/doctype_tree.js"}
# doctype_calendar_js = {"doctype" : "public/js/doctype_calendar.js"}

# Svg Icons
# ------------------
# include app icons in desk
# app_include_icons = "sb_health/public/icons.svg"
web_include_js = ["/assets/frappe/js/frappe-web.bundle.js"]

# Home Pages
# ----------

# application home page (will override Website Settings)
# home_page = "login"

# website user home page (by Role)
# role_home_page = {
# 	"Guest": "/patient_appointment"
# }
role_home_page = {
	"Counter": "/app/health-counter",
	"Nurse": "/app/nurse-page",
	"Doctor": "/app/doctor-page",
	"Patient": "/app/patient_page",
}

# your_app/hooks.py
boot_session = "sb_health.boot.boot_session"

# Generators
# ----------

# automatically create page for each record of this doctype
# website_generators = ["Web Page"]

# automatically load and sync documents of this doctype from downstream apps
# importable_doctypes = [doctype_1]

# Jinja
# ----------

# add methods and filters to jinja environment
jinja = {
	"methods": "sb_health.utils.jinja_methods",
}

# Installation
# ------------

# before_install = "sb_health.install.before_install"
# after_install = "sb_health.install.after_install"

# Uninstallation
# ------------

# before_uninstall = "sb_health.uninstall.before_uninstall"
# after_uninstall = "sb_health.uninstall.after_uninstall"

# Integration Setup
# ------------------
# To set up dependencies/integrations with other apps
# Name of the app being installed is passed as an argument

# before_app_install = "sb_health.utils.before_app_install"
# after_app_install = "sb_health.utils.after_app_install"

# Integration Cleanup
# -------------------
# To clean up dependencies/integrations with other apps
# Name of the app being uninstalled is passed as an argument

# before_app_uninstall = "sb_health.utils.before_app_uninstall"
# after_app_uninstall = "sb_health.utils.after_app_uninstall"

# Desk Notifications
# ------------------
# See frappe.core.notifications.get_notification_config

# notification_config = "sb_health.notifications.get_notification_config"

# Permissions
# -----------
# Permissions evaluated in scripted ways

# permission_query_conditions = {
# 	"Event": "frappe.desk.doctype.event.event.get_permission_query_conditions",
# }
#
# has_permission = {
# 	"Event": "frappe.desk.doctype.event.event.has_permission",
# }

# DocType Class
# ---------------
# Override standard doctype classes

override_doctype_class = {
	"Healthcare Practitioner": "sb_health.overrides.healthcare_practitioner.HealthcarePractitioner",
}

# Document Events
# ---------------
# Hook on document methods and events

doc_events = {
	"Sales Invoice": {
		"on_submit": "sb_health.doc_events.sales_invoice.create_intercompany_entries",
		"on_cancel": "sb_health.doc_events.sales_invoice.create_reverse_intercompany_entries",
        "before_submit": "sb_health.methods.sales_invoice.sales_invoice",
	}
}

# Scheduled Tasks
# ---------------

# scheduler_events = {
# 	"all": [
# 		"sb_health.tasks.all"
# 	],
# 	"daily": [
# 		"sb_health.tasks.daily"
# 	],
# 	"hourly": [
# 		"sb_health.tasks.hourly"
# 	],
# 	"weekly": [
# 		"sb_health.tasks.weekly"
# 	],
# 	"monthly": [
# 		"sb_health.tasks.monthly"
# 	],
# }

# Testing
# -------

# before_tests = "sb_health.install.before_tests"

# Overriding Methods
# ------------------------------
#
override_whitelisted_methods = {
	"healthcare.healthcare.utils.get_appointment_billing_item_and_rate": "sb_health.overrides.utils.modified_get_appointment_billing_item_and_rate",
}
#
# each overriding function accepts a `data` argument;
# generated from the base implementation of the doctype dashboard,
# along with any modifications made in other Frappe apps
# override_doctype_dashboards = {
# 	"Task": "sb_health.task.get_dashboard_data"
# }

# exempt linked doctypes from being automatically cancelled
#
# auto_cancel_exempted_doctypes = ["Auto Repeat"]

# Ignore links to specified DocTypes when deleting documents
# -----------------------------------------------------------

# ignore_links_on_delete = ["Communication", "ToDo"]

# Request Events
# ----------------
# before_request = ["sb_health.utils.before_request"]
# after_request = ["sb_health.utils.after_request"]

# Job Events
# ----------
# before_job = ["sb_health.utils.before_job"]
# after_job = ["sb_health.utils.after_job"]

# User Data Protection
# --------------------

# user_data_fields = [
# 	{
# 		"doctype": "{doctype_1}",
# 		"filter_by": "{filter_by}",
# 		"redact_fields": ["{field_1}", "{field_2}"],
# 		"partial": 1,
# 	},
# 	{
# 		"doctype": "{doctype_2}",
# 		"filter_by": "{filter_by}",
# 		"partial": 1,
# 	},
# 	{
# 		"doctype": "{doctype_3}",
# 		"strict": False,
# 	},
# 	{
# 		"doctype": "{doctype_4}"
# 	}
# ]

# Authentication and authorization
# --------------------------------

# auth_hooks = [
# 	"sb_health.auth.validate"
# ]

# Automatically update python controller files with type annotations for this app.
# export_python_type_annotations = True

# default_log_clearing_doctypes = {
# 	"Logging DocType Name": 30  # days to retain logs
# }


boot_session = "sb_health.boot.boot_session"

# override_whitelisted_methods = {
#     "*": "sb_health.overrides.slow_wrapper"
# }


# Add this to your hooks.py file

website_route_rules = [
	{"from_route": "/news/<path:news_route>", "to_route": "news"},
]
