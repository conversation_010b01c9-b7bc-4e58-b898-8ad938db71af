import frappe
from frappe import _

def sales_invoice(doc, method):
    if method == "before_submit":
        allowed_roles = ["Healthcare Administrator", "Super Admin"]
        current_user = frappe.session.user

        if current_user == "Administrator":
            return

        user_roles = frappe.get_roles(current_user)

        if not any(role in allowed_roles for role in user_roles):
            invoice_discount_applied = (
                doc.get("discount_amount") or
                doc.get("additional_discount_percentage")
            )

            item_discount_applied = any(
                item.get("discount_percentage") or item.get("discount_amount")
                for item in doc.get("items", [])
            )

            if invoice_discount_applied or item_discount_applied:
                frappe.throw(_("You are not authorized to apply discounts on Sales Invoice. Request a admin user to submit this Sales Invoice."))

