frappe.ui.form.on("Sales Invoice", {
	validate(frm) {
		if (frm.doc.custom_apply_ehs) {
			let promises = [];

			frm.doc.items.forEach((item) => {
				let p = frappe.call({
					method: "frappe.client.get_list",
					args: {
						doctype: "Item Price",
						filters: {
							item_code: item.item_code,
							price_list: "EHS",
						},
						fields: ["price_list_rate"],
						limit_page_length: 1,
					},
					callback: function (r) {
						if (r.message && r.message.length > 0) {
							frappe.model.set_value(item.doctype, item.name, "rate", r.message[0].price_list_rate);
						} else {
							// Get rate from original price list (Standard Selling, or as per your default)
							frappe.call({
								method: "frappe.client.get_list",
								args: {
									doctype: "Item Price",
									filters: {
										item_code: item.item_code,
										price_list: "Standard Selling", // Change this if your original is different
									},
									fields: ["price_list_rate"],
									limit_page_length: 1,
								},
								callback: function (res) {
									if (res.message && res.message.length > 0) {
										frappe.model.set_value(item.doctype, item.name, "rate", res.message[0].price_list_rate);
									}
								},
							});
						}
					},
				});
				promises.push(p);
			});
		}
	},
custom_apply_ehs: function (frm) {
		if (!frm.doc.items || frm.doc.items.length === 0) return;

		let target_price_list = frm.doc.custom_apply_ehs ? "EHS" : "Standard Selling";

		frm.doc.items.forEach((item) => {
			frappe.call({
				method: "frappe.client.get_list",
				args: {
					doctype: "Item Price",
					filters: {
						item_code: item.item_code,
						price_list: target_price_list,
					},
					fields: ["price_list_rate"],
					limit_page_length: 1,
				},
				callback: function (r) {
					if (r.message && r.message.length > 0) {
						frappe.model.set_value(item.doctype, item.name, "rate", r.message[0].price_list_rate);
					} else if (target_price_list === "EHS") {
						// fallback to Standard Selling if EHS not found
						frappe.call({
							method: "frappe.client.get_list",
							args: {
								doctype: "Item Price",
								filters: {
									item_code: item.item_code,
									price_list: "Standard Selling",
								},
								fields: ["price_list_rate"],
								limit_page_length: 1,
							},
							callback: function (res) {
								if (res.message && res.message.length > 0) {
									frappe.model.set_value(item.doctype, item.name, "rate", res.message[0].price_list_rate);
								}
							},
						});
					}
				},
			});
		});
	},
	setup: function(frm) {
		const targetPages = ["health-counter", "pharmacy-page"];
		if (
			window.workstationInstance &&
			targetPages.includes(window.workstationInstance.page_name)
		) {
			frm.page.sidebar.remove();

			let hidden_fields = [
				"company",
				"posting_date",
				"posting_time",
				"accounting_dimensions_section",
				"time_sheet_list",
				"scan_barcode",
				// "update_stock",
				"taxes",
				"currency_and_price_list",
				"set_warehouse",
				"set_posting_time",
				"due_date",
				// "is_debit_note",
				"ref_practitioner",
				"service_unit",
				"taxes_section",
				// "is_pos",
				"use_company_roundoff_cost_center",
			];

			if (frm.is_new()) {
				hidden_fields.forEach(function(field) {
					frm.set_df_property(field, "hidden", 1);
				});
			}

			if (window.workstationInstance && !window.workstationInstance.is_opd) {
				frm.set_df_property("advances_section", "collapsible", 0);
				moveFieldBefore(frm, "advances_section", "items");
			}

			moveFieldBefore(frm, "naming_series", "shipping_rule");
			moveFieldBefore(frm, "paid_amount", "taxes_section");
			moveFieldBefore(frm, "payments", "paid_amount");
			moveFieldBefore(frm, "update_stock", "status");

		}
	},
	patient: function(frm) {
		if (!frm.is_new()) return;
		if (!window.workstationInstance) return;

		const targetPages = ["health-counter"];

		if (frm.doc.patient !== window.workstationInstance.selectedPatient) {
			window.workstationInstance.setSelectedPatient(frm.doc.patient);
		}

		if (
			targetPages.includes(window.workstationInstance.page_name) &&
			frm.doc.patient &&
			!frm.doc.is_return
		) {
			frm.refresh_field("patient");
			frm.set_value("update_stock", 0);

			frm.clear_table("items");

			frm.call({
				doc: frm.doc,
				freeze: true,
				freeze_message: ("Fetching items to invoice..."),
				method: "workstation_auto_fill",
				callback: (r) => {
					frappe.db.get_value(
						"Patient",
						frm.doc.patient,
						"inpatient_status",
						(r) => {
							frm.set_value("is_pos", r.inpatient_status ? 0 : 1);
							frm.refresh_field("is_pos");
						}
					);
				},
			});
		}
	},
	onload: function(frm) {
		if (window.workstationInstance) {
			const targetPages = ["health-counter"];

			const now = new Date();
			const hours = now.getHours();
			const minutes = now.getMinutes();
			const day = now.getDay();

			const currentMinutes = hours * 60 + minutes;
			const startOffMinutes = 16 * 60 + 30;
			const endOffMinutes = 7 * 60 + 30;

			const isWeekendOrOffHour =
				day === 6 || // Saturday
				currentMinutes < endOffMinutes ||
				currentMinutes >= startOffMinutes;

			if (isWeekendOrOffHour && frm.is_new() && !frm.doc.custom_apply_ehs) {
				frm.set_value("custom_apply_ehs", 1);
				frm.trigger("custom_apply_ehs");
			}

			if (
				frm.doc.patient !== window.workstationInstance.selectedPatient &&
				frm.is_new() &&
				frm.doc.is_return !== 1
			) {
				frm.set_value("patient", window.workstationInstance.selectedPatient);
				frm.refresh_field("patient");
			}
		}
	},
	refresh: function(frm) {
		const targetPages = ["health-counter", "pharmacy-page"];
		if (
			window.workstationInstance &&
			targetPages.includes(window.workstationInstance.page_name)
		) {
			if (
				frm.doc.patient !== window.workstationInstance.selectedPatient &&
				frm.is_new() &&
				frm.doc.is_return !== 1
			) {
				frm.set_value("patient", window.workstationInstance.selectedPatient);
				frm.refresh_field("patient");
			}
			setTimeout(() => {
				removeButtons(frm, [
					["Delivery", "Create"],
					["Payment", "Create"],
					["Return / Credit Note", "Create"],
					["Payment Request", "Create"],
					["Invoice Discounting", "Create"],
					["Dunning", "Create"],
					["Maintenance Schedule", "Create"],
					["Accounting Ledger", "View"],
				]);
				if (frm.doc.docstatus == 1) {
					frm.add_custom_button(
						"Return / Credit Note",
						() => {
							frappe.call({
								method: "erpnext.accounts.doctype.sales_invoice.sales_invoice.make_sales_return",
								args: {
									source_name: frm.doc.name,
								},
								callback: function(doc) {
									window.workstationInstance.renderNewForm(
										"Sales Invoice",
										"#content-section",
										{},
										doc.message
									);
								},
							});
						},
						"Create"
					);
				}
			}, 500);
		}
		if (frm.doc.patient && frm.is_new()) {
			frm.trigger("get_advances");
		}
	},
	after_save: function(frm) {
		const targetPages = ["health-counter", "pharmacy-page"];
		if (
			window.workstationInstance &&
			targetPages.includes(window.workstationInstance.page_name)
		) {
			frm.page.sidebar.remove();
		}
	},
	on_submit: function(frm) {
		const targetPages = ["health-counter", "pharmacy-page"];
		if (
			window.workstationInstance &&
			targetPages.includes(window.workstationInstance.page_name)
		) {
			window.open(`/printview?doctype=Sales Invoice&name=${frm.doc.name}&format=Cash Counter Sales Invoice`, '_blank');
		}
	},
});

frappe.ui.form.on("Sales Invoice Item", {
	item_code(frm, cdt, cdn) {
		const targetPages = ["health-counter", "pharmacy-page"];
		if (
			window.workstationInstance &&
			targetPages.includes(window.workstationInstance.page_name)
		) {
			const row = frappe.get_doc(cdt, cdn);
			setTimeout(() => {
				if (row.item_tax_template) {
					frappe.call({
						method: "frappe.client.get",
						args: {
							doctype: "Item Tax Template",
							name: row.item_tax_template,
						},
						callback: function(r) {
							if (r.message && r.message.taxes && r.message.taxes.length > 0) {
								const tax_rate = r.message.taxes[0].tax_rate || 0;
								frappe.model.set_value(cdt, cdn, "tax_rate", tax_rate);
							} else {
								frappe.model.set_value(cdt, cdn, "tax_rate", 0);
							}
						},
					});
				} else {
					frappe.model.set_value(cdt, cdn, "tax_rate", 0);
				}
			}, 500);
		}
	},
});
