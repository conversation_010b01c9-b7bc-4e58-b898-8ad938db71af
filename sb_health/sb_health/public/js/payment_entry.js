frappe.ui.form.on('Payment Entry', {
    onload: function(frm) {    
        const targetPages = ["health-counter"];
        
		if (window.workstationInstance && targetPages.includes(window.workstationInstance.page_name)) {
            let hidden_fields = ["accounting_dimensions_section","taxes_and_charges_section","taxes","company"];

            hidden_fields.forEach(function(field) {
                frm.set_df_property(field, 'hidden', 1);
            });
            if (frm.is_new()){
            moveFieldBefore(frm, "naming_series", "project");
            moveFieldBefore(frm, "posting_date", "project");

            if (window.workstationInstance.selectedPatient) {
                frappe.db.get_doc('Patient', window.workstationInstance.selectedPatient)
                    .then(patient => {
                        frappe.run_serially([
                            () => frm.set_value("mode_of_payment", "Cash"),
                            () => frm.set_value("party_type", "Customer"),
                            () => frm.set_value("party", patient.customer),
                            () => frm.set_value("party_name", patient.customer),
                            () => frm.scroll_to_field("paid_amount")
                        ]);
                    })
                    .catch(error => {
                        console.error("Failed to fetch patient:", error);
                        frappe.msgprint("Could not fetch patient details.");
                    });
            }
            }
        }
    },
    refresh: function(frm){
        frm.remove_custom_button("Ledger");
    },
    onload_post_render: function(frm) {
        const targetPages = ["health-counter"];
        if (window.workstationInstance && targetPages.includes(window.workstationInstance.page_name)) {
            if (frm.is_new()){
                frm.refresh_field("paid_to");
                frm.trigger("paid_to");
            }
        }
    },
});
