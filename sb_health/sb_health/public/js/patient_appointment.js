frappe.ui.form.on('Patient Appointment', {
    refresh: function(frm) {
        const targetPages = ["health-counter","nurse-page","doctor-page"];
		if (window.workstationInstance && targetPages.includes(window.workstationInstance.page_name)) {
            setTimeout(() => {
                removeButtons(frm, [
                    ['Patient Encounter', 'Create'],
                    ['Vital Signs', 'Create'],
                    ['Patient History', 'View'],
                ]);
            }, 50);

            frm.add_custom_button("Take Vitals", () => {
                const create_vitals = () => {
                    window.workstationInstance.renderNewForm(
                    "Vital Signs",
                    "#content-section",
                    {
                        doc_vals: {
                            patient: frm.doc.patient,
                            appointment: frm.doc.name,
                        },
                    }
                );}

                window.workstationInstance.lastRenderingFunction = create_vitals;

                create_vitals();
            });

            if (frm.is_new() && frm.doc.patient !== window.workstationInstance.selectedPatient) {
                frm.set_value("patient", window.workstationInstance.selectedPatient);
            }
        };
    },
    patient: function(frm){
        const targetPages = ["nurse-page","doctor-page"];
		if (window.workstationInstance && targetPages.includes(window.workstationInstance.page_name)) {
            if (frm.is_new() && frm.doc.patient !== window.workstationInstance.selectedPatient) {
                window.workstationInstance.setSelectedPatient(frm.doc.patient);
            }
        }
    }
});