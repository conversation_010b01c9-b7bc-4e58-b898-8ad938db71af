frappe.ui.form.on("Emergency", {
	onload: function (frm) {
		console.log("Emergency form onload");
		const targetPages = ["health-counter", "nurse-page", "doctor-page"];

		if (
			window.workstationInstance &&
			targetPages.includes(window.workstationInstance.page_name)
		) {
			const selectedPatient = window.workstationInstance.selectedPatient;

			if (frm.is_new() && selectedPatient) {
				check_inpatient_status_and_set_patient(frm, selectedPatient);
			}
		}
	},

	refresh: function (frm) {
		const targetPages = ["health-counter", "nurse-page", "doctor-page"];

		if (
			window.workstationInstance &&
			targetPages.includes(window.workstationInstance.page_name)
		) {
			const selectedPatient = window.workstationInstance.selectedPatient;

			if (
				frm.is_new() &&
				selectedPatient &&
				frm.doc.patient !== selectedPatient &&
				!frm.doc.vitals &&
				!frm._inpatient_check_done
			) {
				check_inpatient_status_and_set_patient(frm, selectedPatient);
			}
		}
	},

	patient: function (frm) {
		const targetPages = ["health-counter", "nurse-page", "doctor-page"];

		if (
			window.workstationInstance &&
			targetPages.includes(window.workstationInstance.page_name)
		) {
			const patient = frm.doc.patient;

			if (frm.is_new()) {
				check_inpatient_status_and_set_patient(frm, patient);
				window.workstationInstance.setSelectedPatient(patient);
			}
		}
	}
});

function check_inpatient_status_and_set_patient(frm, patient) {
	frappe.db.get_value("Patient", patient, "inpatient_status")
		.then((r) => {
			if (!r || !r.message || !r.message.inpatient_status) {
				frm.set_value("patient", patient);
				frm.refresh_field("patient");
				frm._inpatient_check_done = true;
			} else {
				frappe.msgprint(__("Selected patient is already admitted in the hospital. Please select another patient."));
			}
		});
}
