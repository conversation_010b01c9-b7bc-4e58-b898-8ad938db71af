// Copyright (c) 2025, <PERSON><PERSON><PERSON> and contributors
// For license information, please see license.txt

frappe.ui.form.on("Self Referral Service", {
    onload: function(frm){
        frm.set_value("mobile_no","+977-");
        frm.set_value("order_date",frappe.datetime.now_date());
        frm.set_value("order_time",frappe.datetime.now_time());

        if (window.workstationInstance && window.workstationInstance.page_name === "health-counter") {
            if (window.workstationInstance.selectedPatient) {
                frm.set_value("patient", window.workstationInstance.selectedPatient);
            }
        }
    },
    refresh: function (frm) {
        frm.disable_save();

        frm.page.wrapper.find(".menu-btn-group").hide();

        frm.set_query('template_dt', function() {
			let order_template_doctypes = [
				"Clinical Procedure Template",
				"Observation Template"];
			return {
				filters: {
					name: ['in', order_template_doctypes]
				}
			};
		});

        function update_button_label() {
            frm.clear_custom_buttons();
    
            const label = frm.doc.patient ? "Request" : "Create Patient & Request";
    
            frm.add_custom_button(label, function () {
                validate_mobile(frm);
            
                frappe.call({
                    method: "sb_health.sb_health.doctype.self_referral_service.self_referral_service.quick_request",
                    args: {
                        data: JSON.stringify(frm.doc)
                    },
                    callback: function (r) {
                        if (r.message) {
                            if (window.workstationInstance) {
                                window.workstationInstance.renderForm("Service Request", r.message.service_request, "#content-section");
                            }
                            else{
                                frm.dashboard.set_headline(__('Requested Successfully'));
                            }
                        }
                        else{
                            frm.dashboard.set_headline(__('Request Failed'));
                        }
                    }
                });
        }).addClass("btn-primary");

    }
        update_button_label();
        frm.fields_dict.patient.df.onchange = update_button_label;
    },
    patient: function(frm) {
        if (window.workstationInstance) {
            const targetPages = ["health-counter"];
            if (targetPages.includes(window.workstationInstance.page_name)) {
                window.workstationInstance.setSelectedPatient(frm.doc.patient);
            }
        }
        if (frm.doc.patient) {
            frappe.db.get_doc("Patient", frm.doc.patient).then(doc => {
                frm.set_value("patient_name", doc.patient_name || `${doc.first_name} ${doc.last_name || ''}`.trim());
                frm.set_value("sex", doc.sex);
                frm.set_value("mobile_no", doc.mobile);

                if (doc.dob) {
                    frm.set_df_property("age", "hidden", false);
                    frm.set_value("age", get_age(doc.dob));
                    frm.set_df_property("age", "description", doc.dob);
                }
                else {
                    frm.set_df_property("age", "hidden", true);
                }
    
                frm.set_df_property("patient_name", "read_only", true);
                frm.set_df_property("sex", "read_only", true);
                frm.set_df_property("mobile_no", "read_only", true);
                frm.set_df_property("age", "read_only", true);

            });
        } else {
            frm.set_value("patient_name", "");
            frm.set_value("sex", "");
            frm.set_value("mobile_no", "+977-");
            frm.set_value("age", "");
    
            frm.set_df_property("patient_name", "read_only", false);
            frm.set_df_property("sex", "read_only", false);
            frm.set_df_property("mobile_no", "read_only", false);
            frm.set_df_property("age", "read_only", false);
            frm.set_df_property("age", "description", "");
        }
    },
    mobile_no: function(frm) {
        if (frm.doc.mobile_no && frm.doc.mobile_no.length >= 15) {
            const isValid = validate_mobile(frm);

            if (isValid) {
                frm.set_df_property("mobile_no", "description", 
                    '<span style="color:green;">Valid phone number format.</span>');
            } else {
                if(frm.doc.mobile_no.startsWith("+977-")) {
                    frm.set_df_property("mobile_no", "description", 
                        '<span style="color:red;">Please enter a valid 10-digit Nepali mobile number in the format +977-98XXXXXXXX.</span>');
                }
            }
        } else {
            frm.set_df_property("mobile_no", "description", 
                '');
        }
    },
    validate: function(frm) {
        frappe.validated = true;
    }
});

function validate_mobile(frm) {
    const mobile = frm.doc.mobile_no;

    if (mobile && mobile.length > 0) {
        const parts = mobile.split('-');

        if (parts.length === 2) {
            const countryCode = parts[0];
            const number = parts[1];

            if (countryCode === "+977") {
                const nepalMobileRegex = /^(9)\d{9}$/;
                return nepalMobileRegex.test(number);
            }
        }
    }

    return false;
}

function get_age(dob_str) {
    if (!dob_str) return "";

    const dob = frappe.datetime.str_to_obj(dob_str);
    const today = frappe.datetime.str_to_obj(frappe.datetime.now_date());

    let age = today.getFullYear() - dob.getFullYear();
    const monthDiff = today.getMonth() - dob.getMonth();
    const dayDiff = today.getDate() - dob.getDate();

    if (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)) {
        age -= 1;
    }
    return `${age}`;
}
