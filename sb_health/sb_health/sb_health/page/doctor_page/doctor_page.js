class DoctorWorkStation extends BaseWorkStation {
	constructor(wrapper, templatesPath, templates, layout_html, page_name) {
		super(wrapper, templatesPath, templates, page_name);
		this.layout_html = layout_html;
		this.selectedPatient = "";
		this.preloadDoctypes = ["Sales Invoice", "Payment Entry"];
		this.formManager = new FormManager(this.preloadDoctypes, "#content-section");

		this.actions = {
			"#take-vitals": () => this.renderNewForm("Vital Signs", "#content-section"),
			"#list-vitals": () =>
				this.loadSection("vitals_list", "#content-section", {
					doctype: "Vital Signs",
					filters: { patient: this.selectedPatient },
				}),
			"#make-encounter": () => this.renderNewForm("Patient Encounter", "#content-section"),
			"#list-encounters": () =>
				this.loadSection("encounter_list", "#content-section", {
					doctype: "Patient Encounter",
					filters: {
						patient: this.selectedPatient,
						medical_department: this.medical_department,
					},
				}),
			"#make-appointment": () =>
				this.renderNewForm("Patient Appointment", "#content-section"),
			"#show-appointment": () =>
				this.loadSection("patient_appointment_list", "#content-section", {
					doctype: "Patient Appointment",
					filters: {
						patient: this.selectedPatient,
						department: this.medical_department,
					},
					med: this.medical_department,
				}),
			"#new-emergency-ipd": () => {
				this.renderNewForm("Emergency", "#content-section");
			},
			"#show-emergency": () =>
				this.loadSection("emergency_list", "#content-section", {
					doctype: "Emergency",
					filters: { patient: this.selectedPatient },
				}),
			"#make-ipr": () => this.renderNewForm("Inpatient Record", "#content-section"),
			"#list-iprs": () =>
				this.loadSection("ipr_list", "#content-section", {
					doctype: "Inpatient Record",
					filters: { medical_department: this.medical_department },
				}),
			"#new-material-request": () =>
				this.renderNewForm("Material Request", "#content-section"),
			"#new-delivery-note": () => this.renderNewForm("Delivery Note", "#content-section"),
			"#ipd-transfer": () => this.renderNewForm("Transfer Request", "#content-section"),
			"#all-ipd-transfer": () =>
				this.loadSection("ipd_transfer_list", "#content-section", {
					doctype: "Transfer Request",
					filters: {},
				}),

		};
        	this.initPage("Doctor Work Station");
			this.initSelectedPatient();
			this.attachGlobalEventHandlers();

	}



	renderLayout() {
		super.renderLayout(this.layout_html);
		this.loadSection("button_container", "#button-section");

		this.lastRenderingFunction = () => {
			this.loadSection("patient_appointment_list", "#content-section", {
				doctype: "Patient Appointment",
				filters: {
					patient: this.selectedPatient,
					department: this.medical_department,
				},
				med: this.medical_department,
			});
		};

		this.lastRenderingFunction();

        this.initPatientField();

	}

	initSelectedPatient() {
		if (!$("#selectedPatientValue").length) {
			$("<div>", {
				id: "selectedPatientValue",
				text: "No patient selected",
				"data-value": "",
				style: "display: none;",
			}).appendTo("body");
		}
	}

	initPatientField() {
		const formSection = document.getElementById("form-section");
		if (!formSection) return console.error("Error: form-section not found");

		this.patientField = frappe.ui.form.make_control({
			parent: formSection,
			df: {
				fieldtype: "Link",
				options: "Patient",
				label: "Select Patient",
				fieldname: "patient",
				change: () => this.setSelectedPatient(this.patientField.get_value()),
			},
			render_input: true,
		});
		this.patientField.refresh();
	}

	setSelectedPatient(patient) {
		if (this.selectedPatient === patient) return;

		this.selectedPatient = patient;
		$("#selectedPatientValue").text(patient).attr("data-value", patient);

		if (this.patientField && this.patientField.get_value() !== patient) {
			this.patientField.set_value(patient);
		}

		this.updateSectionsAfterSelection();
	}

    updateSectionsAfterSelection() {
        this.loadSection("patient_preview", "#patient-preview-section", { selectedPatient: this.selectedPatient });
        if (this.lastRenderingFunction) this.lastRenderingFunction();
    }

    renderNewForm(doctype, section) {
        $(section).empty();
        if (this.currentForm) {
			if (this.currentForm.beforeUnloadListener) {
				removeEventListener("beforeunload", this.currentForm.beforeUnloadListener, {
					capture: true,
				});
			}

			frappe.ui.form.off(this.currentForm.doctype, "refresh");
			frappe.ui.form.off(this.currentForm.doctype, "onload");
			frappe.ui.form.off(this.currentForm.doctype, "setup");
			this.currentForm.docname = null;
			this.currentForm.$wrapper = null;
			// this.currentForm.page = null;
			this.currentForm = null;
		}
        frappe.model.with_doctype(doctype, () => {
            let newDoc = frappe.model.get_new_doc(doctype);
            if (this.selectedPatient && ["Patient Appointment", "Patient Encounter", "Vital Signs", "Delivery Note", "Material Request"].includes(doctype)) {
                newDoc.patient = this.selectedPatient;
            }
            new frappe.ui.form.Form(doctype, $(section)).refresh(newDoc.name);
        });
    }
}
function navModification() {
	const logoEl = $(".navbar-brand.navbar-home .app-logo");
	logoEl.wrap(
		'<div class="navbar-brand-wrapper" style="display: flex; align-items: center;"></div>'
	);

	logoEl.parent().append(
		`<span 
			style="
			display: inline-block;
			font-size: 1rem;
			font-weight: 650;
			color: rgba(241, 240, 240, 0.95);
			margin-left: 10px;
			line-height: 1.3;
			text-shadow: 0 1px 2px rgba(0,0,0,0.3);
			"
		>
			Kathmandu National<br>Medical College
		</span>`
	);

	$("#navbar-search").hide();
	$(".search-icon").hide();
}

function addDocumentation() {
    // Remove old if exists
    $("#page-doc-button").remove();
  
    const docButton = $(`
      <div id="page-doc-button" style="
        margin-left: 20px;
        margin-right: 20px;
        cursor: pointer;
        color: #fff;
        background-color: transparent;
        border-radius: 4px;
        font-size: 0.9rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        transition: color 0.3s, background-color 0.3s;
      "
      title="Quick Guide"
      onmouseover="this.style.color='#e0e0e0'"
      onmouseout="this.style.color='#fff'"
      >
        <i class="fa fa-info-circle" style="font-size: 30px;"></i>
      </div>
    `);
  
    // Add it to the navbar
    $(".navbar .container").append(docButton);
  
    // Markdown styling
    $(`<style>
      .doc-guide-content {
        padding: 10px;
        font-family: 'Segoe UI', sans-serif;
        line-height: 1.6;
        font-size: 14px;
        color: #333;
      }
      .doc-guide-content h2, .doc-guide-content h3 {
        color: #038E43;
        margin-top: 1rem;
        margin-bottom: 0.5rem;
        font-weight: 600;
      }
      .doc-guide-content ul {
        padding-left: 20px;
        margin-bottom: 10px;
      }
      .doc-guide-content li {
        margin-bottom: 6px;
      }
      .doc-guide-content code {
        background-color: #f5f5f5;
        padding: 2px 6px;
        border-radius: 3px;
        font-family: monospace;
        color: #d63384;
      }
      .doc-guide-content blockquote {
        background: #f0f0f0;
        padding: 10px;
        border-left: 4px solid #038E43;
        margin: 1rem 0;
        font-style: italic;
        color: #555;
      }
    </style>`).appendTo("head");
  
    // Click action for dialog
    docButton.on("click", function (e) {
      e.preventDefault();
      e.stopPropagation();
  
      const pageName = "doctor-page";
  
      frappe.call({
        method: "frappe.client.get_list",
        args: {
          doctype: "Documentation",
          filters: { page: pageName },
          fields: ["documentation", "name"],
          limit_page_length: 1
        },
        callback: function (res) {
          if (!res.message || res.message.length === 0) {
            frappe.msgprint("No documentation found for this page.");
            return;
          }
  
          const doc = res.message[0];
          const markdown = doc.documentation || "No content available.";
          const html = window.marked ? marked.parse(markdown) : frappe.utils.escape_html(markdown);
  
          const dialog = new frappe.ui.Dialog({
            title: "Quick Guide",
            size: "large",
            primary_action_label: "Close",
            primary_action: () => dialog.hide(),
          });
  
          dialog.$body.html(`<div class="doc-guide-content">${html}</div>`);
          dialog.show();
        }
      });
    });
  }
  
  

const page_name = "doctor-page";
frappe.pages[page_name].on_page_load = function (wrapper) {

    navModification();
    const templates = {
        "button_container": "button_container.html",
        "patient_preview": "patient_preview.html",
        "encounter_list": "encounter_list.html",
        "vitals_list": "vitals_list.html",
        "patient_appointment_list": "patient_appointment_list.html",
        "emergency_list":"emergency_list.html",
        "ipr_list":"ipr_list.html",
        "ipd_transfer_list":"ipd_transfers_list.html",
        "patient_preview_appointment_list": "components/appointment_list.html",
    };
    const templatesPath = "sb_health/templates/pages/doctor/";
    const layout_html = (`
        <div class="container-fluid">
            <div class="row justify-content-center">
            <div class="col-12 col-md-2" id="button-section-container">
                    <div 
                        id="button-section" 
                        class="d-flex flex-column justify-content-start align-items-start p-1"
                        style="
                            background: rgba(255, 255, 255, 0.1);
                            backdrop-filter: blur(12px);
                            border-left: 1px solid rgba(255, 255, 255, 0.2);
                            height: auto;                       "
                    >
                    </div>
                </div>
                
                <div class="col-12 col-md-7" id="content-section-container">
                    <div class="card p-3">
                        <div class="health-sections" id="content-section"></div>
                    </div>
                </div>

                 <div class="col-12 col-md-3" id="side-section-container">
                    <div class="col">
                        <div class="section-buttons" id="form-section"></div>
                        <div class="section-buttons" id="patient-preview-section"></div>
                    </div>
                </div>
            </div>
        </div>
    `);

    window.workstationInstance = new DoctorWorkStation(
        wrapper, 
        templatesPath, 
        templates,
        layout_html,
        page_name);

    if (typeof marked === "undefined") {
        const script = document.createElement("script");
        script.src = "https://cdn.jsdelivr.net/npm/marked/marked.min.js";
        script.onload = function () {
            addDocumentation();
        };
        document.head.appendChild(script);
    } else {
        addDocumentation();
    }
};
