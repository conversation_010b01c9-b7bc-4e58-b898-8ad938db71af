{% set selectedPatient = patient %}
{% set date = date or frappe.utils.today() %}
{% set appointments = [] %}


{% set practitioner = frappe.db.get_value("Healthcare Practitioner", {"user_id": frappe.session.user}, "name") %}


{% if selectedPatient %}
  {% set appointments = frappe.get_all(
    "Patient Appointment",
    fields=["appointment_time", "practitioner", "practitioner_name", "name", "status", "department", "service_unit"],
    filters={"patient": selectedPatient, "appointment_date": date,"practitioner": practitioner}
  ) %}
{% endif %}
 

<div id="patient-preview-appointment-list" class="d-flex flex-column align-items-center">
<div class="mb-3 d-flex align-items-center justify-content-center">
  <span class="text-muted font-weight-semibold mr-3" style="font-size: 15px;">
    Total {{ "appointment" if appointments|length == 1 else "appointments" }}
  </span>
  <span 
    class="badge badge-primary d-flex align-items-center justify-content-center shadow-sm bg-secondary"
    style="width: 25px; height: 25px; border-radius: 50%; font-size: 15px;"
  >
    {{ appointments | length }}
  </span>
</div>

  {% if appointments %}
    <ul class="list-group">
      {% for appt in appointments %}
        <li class="list-group-item" 
            style="cursor: pointer;" 
            onclick="
              window.workstationInstance.renderForm(
                'Patient Appointment', 
                '{{ appt.name }}', 
                '#content-section',
                )"
            onmouseover="this.style.boxShadow='0 2px 6px rgba(0,0,0,0.15)'" 
            onmouseout="this.style.boxShadow='none'">

            {% set appointment_fields = [
              ('Status', appt.status),
              ('Time', appt.appointment_time),
              ('Doctor', appt.practitioner_name ~ ' (' ~ appt.practitioner ~ ')'),
              ('Department', appt.department),
              ('Service Unit', appt.service_unit)
            ] %}

            {% for label, value in appointment_fields %}
              <div class="row mb-2">
                  <div class="col-5"><strong>{{ label }} </strong></div>
                  <div class="col-7">{{ value }}</div>
              </div>
            {% endfor %}

          <button class="btn btn-sm btn-secondary float-end"
                  onclick="event.stopPropagation(); window.workstationInstance.renderNewForm('Patient Encounter', '#content-section', 
                  {doc_vals:{
                    'appointment': '{{ appt.name }}'
                  }})">
            Make Encounter
          </button>
          <button class="btn btn-sm btn-secondary float-end"
                  onclick="event.stopPropagation();print_opd_card('{{ appt.name }}')">
            OPD Card
          </button>

        </li>
      {% endfor %}
    </ul>
  {% else %}

    <p class="text-center text-muted small my-4" style="opacity: 0.6;">
        No appointments scheduled for {{ date }}.
    </p>

  {% endif %}
</div>

<script>

  function print_opd_card(appointment_name){
    frappe.db.get_value("Print Format", { "doc_type": "Patient Appointment"}, "name").then((res) => {
        let defaultFormat = res.message ? res.message.name : 'Default'; 
        frappe.utils.print("Patient Appointment", appointment_name, defaultFormat);
    });
  }

</script>