{% set doctype_name = doctype %}
{% set filter_list = {} %}
{% set include_field = ["patient_name", "signs_date", "signs_time"] %}
{% set ignore_filter_fields = ["temperature","height", "weight", "bmi","pulse","respiratory_rate","tongue","abdomen","reflexes","bp_systolic","bp_diastolic"] %}

{% for key, value in filters.items() %}
    {% if value.strip() %}
        {% set _ = filter_list.update({key: value}) %}
    {% endif %}
{% endfor %}
{% include "opterp_app/templates/pages/workstation_list_template.html" %}
