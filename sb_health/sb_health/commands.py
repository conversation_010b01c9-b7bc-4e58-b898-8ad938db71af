import click
import frappe
from frappe import _
from opterp_app.opterp_app.utils import add_custom_field, update_field_property


@click.command("create-type-in-practitioner")
@click.argument("site")
def create_type_in_practitioner(site):
	try:
		frappe.init(site=site)
		frappe.connect()
		frappe.local.site = site

		field = {
			"doc": "Healthcare Practitioner",
			"after_field": "user_id",
			"field_details": {
				"field_name": "healthcare_practitioner_type",
				"field_type": "Select",
				"label": "Healthcare Practitioner Type",
				"options": "\n".join(["Doctor", "Nurse", "Other"]),
			},
		}

		add_custom_field(
			doc=field["doc"],
			after_field=field["after_field"],
			field_details=field["field_details"],
			section=field.get("section"),
		)
		print("Field 'Practitioner Type' created successfully in Healthcare Practitioner.")

	except Exception as e:
		print(f"Failed to create field: {e}")

	finally:
		frappe.destroy()


@click.command("remove-type-in-practitioner")
@click.argument("site")
def remove_type_in_practitioner(site):
	try:
		frappe.init(site=site)
		frappe.connect()
		frappe.local.site = site

		fieldname = "healthcare_practitioner_type"
		doctype = "Healthcare Practitioner"
		custom_field_name = f"{doctype}-{fieldname}"

		if frappe.db.exists("Custom Field", custom_field_name):
			frappe.delete_doc("Custom Field", custom_field_name)
			frappe.db.commit()
			print(f"Custom Field '{fieldname}' removed from {doctype}")
		else:
			print(f"Custom Field '{fieldname}' does not exist in {doctype}")

	except Exception as e:
		print(f"Failed to remove fields: {e}")

	finally:
		frappe.destroy()


@click.command("create-tax-rate-field-in-invoice-item")
@click.argument("site")
def create_tax_rate_field_in_invoice_item(site):
	try:
		frappe.init(site=site)
		frappe.connect()
		frappe.local.site = site

		field = {
			"doc": "Sales Invoice Item",
			"after_field": "rate",
			"field_details": {
				"field_name": "tax_rate",
				"field_type": "Float",
				"label": "Item Tax Rate",
				"precision": 2,
			},
		}

		add_custom_field(
			doc=field["doc"],
			after_field=field["after_field"],
			field_details=field["field_details"],
		)
		print("Field 'Item Tax rate created in sales invoice item child table")

	except Exception as e:
		print(f"Failed to create field: {e}")

	finally:
		frappe.destroy()


@click.command("create-emergency-link-in-vitals")
@click.argument("site")
def create_emergency_link_in_vitals(site):
	try:
		frappe.init(site=site)
		frappe.connect()
		frappe.local.site = site

		field = {
			"doc": "Vital Signs",
			"after_field": "encounter",
			"field_details": {
				"field_name": "emergency",
				"label": "Emergency",
				"field_type": "Link",
				"options": "Emergency",
			},
		}

		add_custom_field(
			doc=field["doc"],
			after_field=field["after_field"],
			field_details=field["field_details"],
		)

		print("Field 'Emergency' created successfully in Vital Signs.")
	except Exception as e:
		print(f"Failed to create field: {e}")
	finally:
		frappe.destroy()


@click.command("create-emergency-link-in-encounter")
@click.argument("site")
def create_emergency_link_in_encounter(site):
	try:
		frappe.init(site=site)
		frappe.connect()
		frappe.local.site = site

		field = {
			"doc": "Patient Encounter",
			"after_field": "appointment_type",
			"field_details": {
				"field_name": "emergency",
				"label": "Emergency",
				"field_type": "Link",
				"options": "Emergency",
			},
		}

		add_custom_field(
			doc=field["doc"],
			after_field=field["after_field"],
			field_details=field["field_details"],
		)

		print("Field 'Emergency' created successfully in Inpatient Record.")
	except Exception as e:
		print(f"Failed to create field: {e}")
	finally:
		frappe.destroy()


@click.command("create-emergency-link-in-inpatient-record")
@click.argument("site")
def create_emergency_link_in_inpatient_record(site):
	try:
		frappe.init(site=site)
		frappe.connect()
		frappe.local.site = site

		field = {
			"doc": "Inpatient Record",
			"after_field": "status",
			"field_details": {
				"field_name": "emergency",
				"label": "Emergency",
				"field_type": "Link",
				"options": "Emergency",
			},
		}

		add_custom_field(
			doc=field["doc"],
			after_field=field["after_field"],
			field_details=field["field_details"],
		)

		print("Field 'Emergency' created successfully in Inpatient Encounter.")
	except Exception as e:
		print(f"Failed to create field: {e}")
	finally:
		frappe.destroy()

@click.command("create-spo2-in-vitals")
@click.argument("site")
def create_spo2_in_vitals(site):
	try:
		frappe.init(site=site)
		frappe.connect()
		frappe.local.site = site

		field = {
			"doc": "Vital Signs",
			"after_field": "respiratory_rate",
			"field_details": {
				"field_name": "spo2_level",
				"label": "SPo2 level",
				"field_type": "Data",
			},
		}

		add_custom_field(
			doc=field["doc"],
			after_field=field["after_field"],
			field_details=field["field_details"],
		)

		print("Field 'Spo2' created successfully in Vital Signs.")
	except Exception as e:
		print(f"Failed to create field: {e}")
	finally:
		frappe.destroy()

@click.command("create-gbrs-in-vitals")
@click.argument("site")
def create_gbrs_in_vitals(site):
	try:
		frappe.init(site=site)
		frappe.connect()
		frappe.local.site = site

		field = {
			"doc": "Vital Signs",
			"after_field": "respiratory_rate",
			"field_details": {
				"field_name": "gbrs_level",
				"label": "GBRS level",
				"field_type": "Data",
			},
		}

		add_custom_field(
			doc=field["doc"],
			after_field=field["after_field"],
			field_details=field["field_details"],
		)

		print("Field 'GBRS' created successfully in Vital Signs.")
	except Exception as e:
		print(f"Failed to create field: {e}")
	finally:
		frappe.destroy()


@click.command("create-all-custom-fields")
@click.argument("site")
def create_all_custom_fields(site):
	print("Starting creation of all custom fields...")

	commands = [
		("create_type_in_practitioner", create_type_in_practitioner),
		("create_tax_rate_field_in_invoice_item", create_tax_rate_field_in_invoice_item),
		("create_emergency_link_in_vitals", create_emergency_link_in_vitals),
		("create_emergency_link_in_encounter", create_emergency_link_in_encounter),
		("create_emergency_link_in_inpatient_record", create_emergency_link_in_inpatient_record),
	]

	for label, cmd in commands:
		try:
			print(f"Running: {label}")
			cmd.main(args=(site,))
		except Exception as e:
			print(f"[FAILED] {label}: {e}")

	print("Command Execution Completed.")


@click.command("fix-lab")
@click.argument("site")
def fix_lab(site):
	try:
		frappe.init(site=site)
		frappe.connect()
		frappe.local.site = site

		observation_templates = frappe.get_all(
			"Observation Template",
			filters={"is_billable": 1},
			fields=["name", "item_code", "rate"]
		)

		for template in observation_templates:
			item_code = template.item_code
			rate = template.rate

			if not item_code or not rate or float(rate) == 0:
				continue

			existing_price = frappe.get_all(
				"Item Price",
				filters={
					"item_code": item_code,
					"price_list": "Standard Selling",
					"selling": 1
				},
				fields=["name"]
			)

			if existing_price:
				price_doc = frappe.get_doc("Item Price", existing_price[0].name)
				price_doc.price_list_rate = rate
				price_doc.save()
				frappe.msgprint(f"Updated Item Price for {item_code}")
			else:
				frappe.get_doc({
					"doctype": "Item Price",
					"item_code": item_code,
					"price_list": "Standard Selling",
					"price_list_rate": rate,
					"selling": 1
				}).insert()
				frappe.msgprint(f"Created Item Price for {item_code}")

		frappe.db.commit()
		frappe.msgprint("✔ Item Price sync complete from Observation Templates.")
	except Exception as e:
		print(f"Failed to create field: {e}")
	finally:
		frappe.destroy()

commands = [
	create_type_in_practitioner,
	remove_type_in_practitioner,
	create_tax_rate_field_in_invoice_item,
	create_emergency_link_in_vitals,
	create_emergency_link_in_encounter,
	create_emergency_link_in_inpatient_record,
	create_all_custom_fields,
	fix_lab,
	create_gbrs_in_vitals,
	create_spo2_in_vitals
]