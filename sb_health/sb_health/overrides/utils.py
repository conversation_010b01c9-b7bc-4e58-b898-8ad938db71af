import json
import frappe
from healthcare.healthcare.utils import get_appointment_type_billing_details, get_healthcare_service_item, get_practitioner_billing_details, throw_config_appointment_type_charge, throw_config_service_item


@frappe.whitelist()
def modified_get_appointment_billing_item_and_rate(doc):
    if isinstance(doc, str):
        doc = json.loads(doc)
        doc = frappe.get_doc(doc)

    service_item = None
    practitioner_charge = None
    department = (
        doc.medical_department if doc.doctype == "Patient Encounter" else doc.department
    )
    service_unit = doc.service_unit if doc.doctype == "Patient Appointment" else None

    is_inpatient = doc.inpatient_record

    if doc.get("practitioner"):
        service_item, practitioner_charge = get_practitioner_billing_details(
            doc.practitioner, is_inpatient
        )

    if not service_item and doc.get("appointment_type"):
        service_item, appointment_charge = get_appointment_type_billing_details(
            doc.appointment_type,
            department if department else service_unit,
            is_inpatient,
        )
        if not practitioner_charge:
            practitioner_charge = appointment_charge

    if not service_item:
        service_item = get_healthcare_service_item(is_inpatient)

    if not service_item:
        throw_config_service_item(is_inpatient)

    # if not practitioner_charge and doc.get("practitioner"):
    # 	throw_config_practitioner_charge(is_inpatient, doc.practitioner)

    # if not practitioner_charge and not doc.get("practitioner"):
        # throw_config_appointment_type_charge(is_inpatient, doc.appointment_type)

    return {"service_item": service_item, "practitioner_charge": practitioner_charge}
