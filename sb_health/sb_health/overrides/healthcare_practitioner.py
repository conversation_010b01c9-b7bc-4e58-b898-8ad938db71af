from erpnext.accounts.party import validate_party_accounts
import frappe
from frappe import _
from healthcare.healthcare.doctype.healthcare_practitioner.healthcare_practitioner import (
    HealthcarePractitioner as OldHealthcarePractitioner,
    validate_service_item,
)
from erpnext.accounts.party import validate_party_accounts
from healthcare.healthcare.doctype.healthcare_practitioner.healthcare_practitioner import validate_service_item


class HealthcarePractitioner(OldHealthcarePractitioner):
    def validate(self):
        self.set_full_name()
        validate_party_accounts(self)

        if self.inpatient_visit_charge_item and not self.inpatient_visit_charge == 0:
            validate_service_item(
                self.inpatient_visit_charge_item,
                "Configure a service Item for Inpatient Consulting Charge Item"
            )
            if not self.inpatient_visit_charge:
                frappe.throw(
                    _(
                        "Inpatient Consulting Charge is mandatory if you are setting Inpatient Consulting Charge Item"
                    ),
                    frappe.MandatoryError,
                )

        if self.op_consulting_charge_item:
            validate_service_item(
                self.op_consulting_charge_item,
                "Configure a service Item for Outpatient Consulting Charge Item"
            )
            if not self.op_consulting_charge:
                frappe.throw(
                    _(
                        "Outpatient Consulting Charge is mandatory if you are setting Outpatient Consulting Charge Item"
                    ),
                    frappe.MandatoryError,
                )

    def on_update(self):
        if self.user_id and not self.healthcare_practitioner_type == "Nurse":
            frappe.permissions.add_user_permission("Healthcare Practitioner", self.name, self.user_id)

    def validate(self):
        self.set_full_name()
        validate_party_accounts(self)
        if self.inpatient_visit_charge_item:
            validate_service_item(
                self.inpatient_visit_charge_item,
                "Configure a service Item for Inpatient Consulting Charge Item",
            )
            # if not self.inpatient_visit_charge:
            # 	frappe.throw(
            # 		_(
            # 			"Inpatient Consulting Charge is mandatory if you are setting Inpatient Consulting Charge Item"
            # 		),
            # 		frappe.MandatoryError,
            # 	)

        if self.op_consulting_charge_item:
            validate_service_item(
                self.op_consulting_charge_item,
                "Configure a service Item for Outpatient Consulting Charge Item",
            )
            # if not self.op_consulting_charge:
            # 	frappe.throw(
            # 		_(
            # 			"Outpatient Consulting Charge is mandatory if you are setting Outpatient Consulting Charge Item"
            # 		),
            # 		frappe.MandatoryError,
            # 	)

        if self.user_id:
            self.validate_user_id()
        else:
            existing_user_id = frappe.db.get_value("Healthcare Practitioner", self.name, "user_id")
            if existing_user_id:
                frappe.permissions.remove_user_permission(
                    "Healthcare Practitioner", self.name, existing_user_id
                )

        self.validate_practitioner_schedules()
