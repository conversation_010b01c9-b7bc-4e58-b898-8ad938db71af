import frappe
import qrcode
import base64
from io import BytesIO
from sb_health.methods.patient import patient_balance
from datetime import date as dt_date


def check_gate_pass_eligibility(patient):
    invoices = frappe.get_all("Sales Invoice", 
        filters={"patient": patient.name, "docstatus": 1},
        fields=["name", "outstanding_amount"]
    )

    for invoice in invoices:
        if invoice.outstanding_amount > 0:
            return False

    return True


def get_last_inpatient_record(patient):
    inpatient = frappe.get_all(
        "Inpatient Record",
        filters={"patient": patient},
        order_by="admitted_datetime desc",
        limit=1
    )
    if inpatient:
        return inpatient[0].name
    else:
        return None
    
def get_gatepass_for_inpatient(inpatient_record):
    return frappe.db.get_value("Gatepass", {"inpatient_record": inpatient_record})


def generate_qr_code(doc):
    data = f"{doc.patient_name} | {doc.blood_group} | {doc.dob}"
    
    qr = qrcode.QRCode(box_size=4, border=1)
    qr.add_data(data)
    qr.make(fit=True)
    img = qr.make_image(fill="black", back_color="white")
    
    buffer = BytesIO()
    img.save(buffer, format="PNG")
    qr_base64 = base64.b64encode(buffer.getvalue()).decode()
    
    doc.qr_code_base64 = f"data:image/png;base64,{qr_base64}"


import base64
from io import BytesIO
from barcode import get_barcode_class
from barcode.writer import ImageWriter

def generate_barcode(doc):
    # Use any unique identifier (e.g., patient name or ID)
    code = f"{doc.patient}"

    barcode_class = get_barcode_class('code128')  # or 'code39', 'ean13' etc.
    barcode_obj = barcode_class(code, writer=ImageWriter())

    buffer = BytesIO()
    barcode_obj.write(buffer)

    barcode_base64 = base64.b64encode(buffer.getvalue()).decode()
    doc.barcode_base64 = f"data:image/png;base64,{barcode_base64}"


def get_invoice_details(invoice_names):
    result = []
    for name in invoice_names:
        invoice = frappe.get_doc("Sales Invoice", name)
        result.append({
            "name": invoice.name,
            "customer": invoice.customer,
            "posting_date": invoice.posting_date
        })
    return result

def get_grouped_invoice_items(invoice_names):
    grouped = {}
    group_totals = {}
    
    # Overall totals
    total_amount = 0.0
    total_discount = 0.0
    total_tax = 0.0
    total_net_total = 0.0
    taxable_total = 0.0
    nontaxable_total = 0.0

    for name in invoice_names:
        invoice = frappe.get_doc("Sales Invoice", name)
        for item in invoice.items:
            item_doc = frappe.get_doc("Item", item.item_name)
            group = item_doc.item_group

            if group not in grouped:
                grouped[group] = []
                group_totals[group] = {
                    "qty": 0.0,
                    "rate": 0.0,
                    "amount": 0.0,
                    "discount": 0.0,
                    "tax": 0.0,
                    "net_total": 0.0
                }

            item_amount = float(item.amount)
            item_discount = float(item.discount_amount)
            item_tax = item_amount * item.tax_rate * 0.01
            net_total = item_amount + item_tax - item_discount

            # Append item details
            grouped[group].append({
                "invoice_name": invoice.name,
                "posting_date": str(invoice.posting_date),
                "customer": invoice.customer,
                "item_name": item.item_name,
                "qty": float(item.qty),
                "rate": float(item.rate),
                "amount": item_amount,
                "item_group": group,
                "item_discount": item_discount,
                "tax_rate": item_tax,
                "item_tax_template": item.item_tax_template,
                "balance": patient_balance(invoice.customer)
            })

            # Update group totals
            group_totals[group]["qty"] += float(item.qty)
            group_totals[group]["rate"] += float(item.rate)
            group_totals[group]["amount"] += item_amount
            group_totals[group]["discount"] += item_discount
            group_totals[group]["tax"] += item_tax
            group_totals[group]["net_total"] += net_total

            # Update overall totals
            total_amount += item_amount
            total_discount += item_discount
            total_tax += item_tax
            total_net_total += net_total

            if item.item_tax_template:
                taxable_total += item_amount
            else:
                nontaxable_total += item_amount

    return {
        "grouped_items": grouped,
        "group_totals": group_totals,
        "total_amount": total_amount,
        "total_discount": total_discount,
        "total_tax": total_tax,
        "total_net_total": total_net_total,
        "taxable_total": taxable_total,
        "nontaxable_total": nontaxable_total,
    }


def age_from_date(dob):
    if not dob:
        return None

    today = dt_date.today()
    age = today.year - dob.year - ((today.month, today.day) < (dob.month, dob.day))
    return age