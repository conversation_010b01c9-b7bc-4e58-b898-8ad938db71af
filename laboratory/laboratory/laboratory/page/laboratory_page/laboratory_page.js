class LaboratoryWorkStation extends BaseWorkStation {
	constructor(wrapper, templatesPath, templates, layout_html, page_name) {
		super(wrapper, templatesPath, templates, page_name);
		this.layout_html = layout_html;
		this.selectedPatient = "";
		this.preloadDoctypes = ["Sales Invoice", "Payment Entry"];
		this.formManager = new FormManager(this.preloadDoctypes, "#content-section");

		this.actions = {
			"#show-observation-template": () =>
				this.loadSection("obeservation_list", "#content-section", {
					doctype: "Observation Template",
					filters: {},
				}),
			"#service-request": () =>
				this.loadSection("service_request_list", "#content-section", {
					doctype: "Service Request",
					filters: { patient: window.workstationInstance.selectedPatient },
				}),
			"#sample-collection": () =>
				this.loadSection("sample_collection_list", "#content-section", {
					doctype: "Sample Collection",
					filters: { patient: window.workstationInstance.selectedPatient },
				}),
			"#diagonistic-report": () =>
				this.loadSection("diagonistic_report_list", "#content-section", {
					doctype: "Diagnostic Report",
					filters: { patient: window.workstationInstance.selectedPatient },
				}),
		};

		this.initPage("Laboratory Work Station");
		this.initSelectedPatient();
		this.initPatientField();
		this.attachGlobalEventHandlers();
	}

	renderLayout() {
		super.renderLayout(this.layout_html);
		this.loadSection("button_container", "#button-section");
		this.lastRenderingFunction = () => {
			this.loadSection("service_request_list", "#content-section", {
				doctype: "Service Request",
				filters: {},
			});
		};

		this.lastRenderingFunction();
	}

	initSelectedPatient() {
		if (!$("#selectedPatientValue").length) {
			$("<div>", {
				id: "selectedPatientValue",
				text: "No patient selected",
				"data-value": "",
				style: "display: none;",
			}).appendTo("body");
		}
	}

	initPatientField() {
		const formSection = document.getElementById("form-section");
		if (!formSection) return console.error("Error: form-section not found");

		this.patientField = frappe.ui.form.make_control({
			parent: formSection,
			df: {
				fieldtype: "Link",
				options: "Patient",
				label: "Select Patient",
				fieldname: "patient",
				change: () => this.setSelectedPatient(this.patientField.get_value()),
			},
			render_input: true,
		});
		this.patientField.refresh();
	}

	setSelectedPatient(patient) {
		if (this.selectedPatient === patient) return;

		this.selectedPatient = patient;
		$("#selectedPatientValue").text(patient).attr("data-value", patient);

		if (this.patientField && this.patientField.get_value() !== patient) {
			this.patientField.set_value(patient);
		}

		this.updateSectionsAfterSelection();
	}

	updateSectionsAfterSelection() {
		this.loadSection("patient_preview", "#patient-preview-section", {
			selectedPatient: this.selectedPatient,
		});

		if (this.currentForm) {
			this.currentForm.set_value("patient", this.selectedPatient);
			this.currentForm.refresh_field("patient");
		} else {
			if (this.lastRenderingFunction) this.lastRenderingFunction();
		}
	}
}

function navModification() {
	const logoEl = $(".navbar-brand.navbar-home .app-logo");
	logoEl.wrap(
		'<div class="navbar-brand-wrapper" style="display: flex; align-items: center;"></div>'
	);

	logoEl.parent().append(
		`<span 
			style="
			display: inline-block;
			font-size: 1rem;
			font-weight: 650;
			color: rgba(241, 240, 240, 0.95);
			margin-left: 10px;
			line-height: 1.3;
			text-shadow: 0 1px 2px rgba(0,0,0,0.3);
			"
		>
			Kathmandu National<br>Medical College
		</span>`
	);
	$("#navbar-search").hide();
	$(".search-icon").hide();
}

const page_name = "laboratory-page";
frappe.pages[page_name].on_page_load = function (wrapper) {
	navModification();

	const templates = {
		button_container: "button_container.html",
		patient_preview: "patient_preview.html",
		obeservation_list: "observation_template_list.html",
		service_request_list: "service_request_list.html",
		sample_collection_list: "sample_collection_list.html",
		diagonistic_report_list: "diagonistic_report_list.html",
		patient_preview_service_request_list: "components/service_request_list.html",
	};
	const templatesPath = "/templates/pages/laboratory/";
	const layout_html = `
        <div class="container-fluid">
            <div class="row">
            <div class="col-12 col-md-2" id="button-section-container">
                    <div 
                        id="button-section" 
                        class="d-flex flex-column justify-content-start align-items-start p-1"
                        style="
                            background: rgba(255, 255, 255, 0.1);
                            backdrop-filter: blur(12px);
                            border-left: 1px solid rgba(255, 255, 255, 0.2);
                            height: auto;
                        "
                    >
                    </div>
                </div>
                
                <div class="col-12 col-md-7">
                    <div class="card p-3">
                        <div class="health-sections" id="content-section"></div>
                    </div>
                </div>

                 <div class="col-12 col-md-3">
                    <div class="col">
                        <div class="section-buttons" id="form-section"></div>
                        <div class="section-buttons" id="patient-preview-section"></div>
                    </div>
                </div>
    
            </div>
        </div>
		<div id="geofinity-footer" style="
			position: fixed;
			bottom: 5px;
			right: 5px;
			font-size: 0.6rem;
			color: rgba(37, 34, 34, 0.6);
			background: rgba(255, 255, 255, 0.11);
			padding: 3px 6px;
			border-radius: 2px;
			backdrop-filter: blur(4px);
			box-shadow: 0 1px 2px rgba(24, 51, 12, 0.3);
			z-index: 0;
		">
			Developed by Geofinity Solutions Pvt. Ltd.
		</div>
    `;

	window.workstationInstance = new LaboratoryWorkStation(
		wrapper,
		templatesPath,
		templates,
		layout_html,
		page_name
	);

	window.workstationInstance.beforeRouteChange = function (...route) {
		window.workstationInstance.removePageStyles();
	};
};

const originalSetRoute = frappe.set_route;

frappe.set_route = function (...args) {
	try {
		if (typeof window.workstationInstance?.beforeRouteChange === "function") {
			// window.workstationInstance.beforeRouteChange(...args);
			if (args.length === 1) {
				const [, raw_doctype, docname] = args[0].split("/").filter(Boolean);
				const doctype = raw_doctype
					.split("-")
					.map((part) => part.charAt(0).toUpperCase() + part.slice(1))
					.join(" ");

				window.workstationInstance.renderForm(
					doctype,
					decodeURIComponent(docname),
					"#content-section"
				);
				return;
			} else if (args.length === 3 && args[0] === "List") {
				window.workstationInstance.loadSection(args[1], "#content-section");
				return;
			} else if (args.length === 3 && args[0] === "Form") {
				console.log("Form route detected", args);
				console.log("route options", frappe.route_options);
				window.workstationInstance.renderNewForm(args[1], "#content-section", {});
				return;
			} else {
				console.warn("Unsupported route format", args);
			}
		}
	} catch (e) {
		console.warn("beforeRouteChange threw an error", e);
	}

	return originalSetRoute.apply(this, args);
};
