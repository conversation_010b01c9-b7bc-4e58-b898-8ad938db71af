// // Copyright (c) 2020, earthians and contributors
// // For license information, please see license.txt
// // {% include "healthcare/public/js/service_request.js" %}

// frappe.ui.form.on('Service Request', {
// 	make_clinical_procedure: function(frm) {
// 		frappe.call({
// 			method: 'healthcare.healthcare.doctype.service_request.service_request.make_clinical_procedure',
// 			args: { service_request: frm.doc },
// 			freeze: true,
// 			callback: function(r) {
// 				var doclist = frappe.model.sync(r.message);
// 				frappe.set_route('Form', doclist[0].doctype, doclist[0].name);
// 			}
// 		});
// 	},

// 	make_lab_test: function(frm) {
// 		frappe.call({
// 			method: 'healthcare.healthcare.doctype.service_request.service_request.make_lab_test',
// 			args: { service_request: frm.doc },
// 			freeze: true,
// 			callback: function(r) {
// 				var doclist = frappe.model.sync(r.message);
// 				frappe.set_route('Form', doclist[0].doctype, doclist[0].name);
// 			}
// 		});
// 	},

// 	make_therapy_session: function(frm) {
// 		frappe.call({
// 			method: 'healthcare.healthcare.doctype.service_request.service_request.make_therapy_session',
// 			args: { service_request: frm.doc },
// 			freeze: true,
// 			callback: function(r) {
// 				var doclist = frappe.model.sync(r.message);
// 				frappe.set_route('Form', doclist[0].doctype, doclist[0].name);
// 			}
// 		});
// 	},

// 	make_observation: function(frm) {
// 		frappe.call({
// 			method: 'healthcare.healthcare.doctype.service_request.service_request.make_observation',
// 			args: { service_request: frm.doc },
// 			freeze: true,
// 			callback: function(r) {
// 				if (r.message) {
// 					var title = "";
// 					var indicator =  "info";
// 					if (r.message[2]) {
// 						title = `${r.message[0]} is already created`
// 					} else {
// 						title = `${r.message[0]} is created`
// 						indicator = "green"
// 					}
// 					frappe.show_alert({
// 						message: __("{0}", [title]),
// 						indicator: indicator,
// 					});
// 					frappe.set_route('Form', r.message[1], r.message[0]);
// 				}
// 			}
// 		});
// 	},
// });