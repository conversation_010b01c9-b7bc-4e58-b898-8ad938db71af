{"actions": [], "creation": "2025-04-10 10:00:00", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["transaction_type", "transaction_id", "posting_date", "mode_of_payment", "payment_type", "amount", "is_return"], "fields": [{"fieldname": "transaction_type", "fieldtype": "Select", "in_list_view": 1, "label": "Transaction Type", "options": "\nSales Invoice\nPayment Entry\nSales Invoice Return"}, {"fieldname": "transaction_id", "fieldtype": "Dynamic Link", "in_list_view": 1, "label": "Transaction ID", "options": "transaction_type"}, {"fieldname": "posting_date", "fieldtype": "Date", "in_list_view": 1, "label": "Posting Date", "read_only": 1}, {"fieldname": "mode_of_payment", "fieldtype": "Link", "in_list_view": 1, "label": "Mode of Payment", "options": "Mode of Payment", "read_only": 1}, {"fieldname": "payment_type", "fieldtype": "Select", "in_list_view": 1, "label": "Payment Type", "options": "\nReceive\nPay", "read_only": 1}, {"fieldname": "amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Amount", "read_only": 1}, {"default": "0", "fieldname": "is_return", "fieldtype": "Check", "label": "Is Return", "read_only": 1}], "istable": 1, "links": [], "modified": "2025-08-05 14:04:34.687418", "modified_by": "Administrator", "module": "Opterp App", "name": "Cashier Closing Transaction", "owner": "Administrator", "permissions": [], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}