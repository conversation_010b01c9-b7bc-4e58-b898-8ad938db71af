// Copyright (c) 2025, <PERSON><PERSON><PERSON> and contributors
// For license information, please see license.txt

frappe.ui.form.on('Counter Closing Entry', {
    setup: function(frm) {
        if (!frm.doc.period_end_date) {
            frm.set_value('period_end_date', frappe.datetime.now_datetime());
        }
        
        if (!frm.doc.posting_date) {
            frm.set_value('posting_date', frappe.datetime.get_today());
        }
    },
    
    refresh: function(frm) {
        if (frm.doc.docstatus === 0) {
            frm.add_custom_button(__('Get Transactions'), function() {
                frm.clear_table('transactions');
                frm.clear_table('payment_reconciliation');
                frm.events.get_payment_reconciliation(frm);
            });
        }
        frm.set_query('counter_opening_entry', function() {
            return {
                filters: {
                    'cashier': frm.doc.cashier,
                    'docstatus': 1,
                    'status': 'Open'
                }
            };
        });
    },
    
    cashier: function(frm) {
        if (frm.doc.cashier && !frm.doc.counter_opening_entry) {
            // Fetch the last open Counter Opening entry for this cashier
            frappe.db.get_list('Counter Opening Entry', {
                filters: {
                    'cashier': frm.doc.cashier,
                    'docstatus': 1,
                    'status': 'Open'
                },
                fields: ['name'],
                limit: 1
            }).then(result => {
                if (result && result.length > 0) {
                    frm.set_value('counter_opening_entry', result[0].name);
                } else {
                    frappe.msgprint(__('No open cashier entry found for this cashier'));
                }
            });
        }
    },
    
    counter_opening_entry: function(frm) {
        if (frm.doc.counter_opening_entry) {
            frappe.db.get_doc('Counter Opening Entry', frm.doc.counter_opening_entry)
                .then(doc => {
                    frm.set_value('cashier', doc.cashier);
                    frm.set_value('company', doc.company);
                    frm.set_value('pos_profile', doc.pos_profile);
                    frm.set_query('pos_profile', function() {
                        return {
                            filters: {
                                'name': doc.pos_profile
                            }
                        };
                    });
                    frm.set_value('period_start_date', doc.period_start_date);
                });
        }
    },
    
    get_payment_reconciliation: function(frm) {
        if (!frm.doc.counter_opening_entry) {
            frappe.msgprint(__('Please select a Counter Opening Entry'));
            return;
        }
        
        frm.call({
            doc: frm.doc,
            method: 'get_payment_reconciliation',
            callback: function(r) {
                if (r.message) {
                    frm.refresh_fields();
                    frm.save();
                }
            }
        });
    }
});
