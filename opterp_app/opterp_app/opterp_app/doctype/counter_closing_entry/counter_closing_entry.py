# Copyright (c) 2025, <PERSON><PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe import _
from frappe.model.document import Document
from frappe.utils import flt, getdate, now_datetime, get_datetime

class CounterClosingEntry(Document):
    def validate(self):
        self.validate_opening_entry()
        if not self.payment_reconciliation:
            self.get_payment_reconciliation()
        self.calculate_totals()
        
    def before_submit(self):
        self.update_opening_entry()
        
    def on_cancel(self):
        self.update_opening_entry(for_cancel=True)
        
    def validate_opening_entry(self):
        if not self.counter_opening_entry:
            frappe.throw(_("Please select a Counter Opening Entry"))
            
        opening_entry = frappe.get_doc("Counter Opening Entry", self.counter_opening_entry)
        
        if opening_entry.docstatus != 1:
            frappe.throw(_("Selected Counter Opening Entry is not submitted"))
            
        if opening_entry.status != "Open":
            frappe.throw(_("Selected Counter Opening Entry is already closed"))
            
        if opening_entry.cashier != self.cashier:
            frappe.throw(_("Cashier in Opening Entry does not match with selected Cashier"))
            
        if opening_entry.pos_profile != self.pos_profile:
            frappe.throw(_("POS Profile in Opening Entry does not match with selected POS Profile"))
        
        # Set period start date from opening entry
        self.period_start_date = opening_entry.period_start_date
            
    def update_opening_entry(self, for_cancel=False):
        opening_entry = frappe.get_doc("Counter Opening Entry", self.counter_opening_entry)
        opening_entry.db_set("counter_closing_entry", self.name if not for_cancel else None)
        opening_entry.db_set("status", "Closed" if not for_cancel else "Open")
    
    @frappe.whitelist()
    def get_payment_reconciliation(self):
        # Get opening balance details
        opening_entry = frappe.get_doc("Counter Opening Entry", self.counter_opening_entry)
        
        # Create payment reconciliation entries
        for detail in opening_entry.balance_details:
            self.append("payment_reconciliation", {
                "mode_of_payment": detail.mode_of_payment,
                "opening_amount": detail.opening_amount,
                "expected_amount": detail.opening_amount,
                "closing_amount": detail.opening_amount
            })
            
        # Get all payment entries and sales invoices
        self.get_payment_entries()
        
    def get_payment_entries(self):
        # Get all payment entries and sales invoices for the cashier
        entries = self.fetch_cashier_payment_entries()
        
        # Process entries and update payment reconciliation
        for entry in entries:
            payment_row = self.get_payment_row(entry.mode_of_payment)
            
            if payment_row:
                # Add transaction to the transactions table
                self.append("transactions", {
                    "transaction_type": entry.voucher_type,
                    "transaction_id": entry.voucher_no,
                    "posting_date": entry.posting_date,
                    "mode_of_payment": entry.mode_of_payment,
                    "payment_type": entry.get("payment_type", ""),
                    "amount": entry.amount,
                    "is_return": entry.get("is_return", 0)
                })
                
                # Update expected amount in payment reconciliation
                payment_row.expected_amount += flt(entry.amount)
                payment_row.closing_amount = payment_row.expected_amount
                
    def get_payment_row(self, mode_of_payment):
        for row in self.payment_reconciliation:
            if row.mode_of_payment == mode_of_payment:
                return row
                
        # If mode of payment not found, create a new row
        new_row = self.append("payment_reconciliation", {
            "mode_of_payment": mode_of_payment,
            "opening_amount": 0,
            "expected_amount": 0,
            "closing_amount": 0
        })
        
        return new_row
        
    def fetch_cashier_payment_entries(self):
        # Get period start date from opening entry
        opening_entry = frappe.get_doc("Counter Opening Entry", self.counter_opening_entry)
        from_datetime = get_datetime(opening_entry.period_start_date)
        to_datetime = get_datetime(self.period_end_date)
        
        # Get already processed transactions
        processed_transactions = self.get_processed_transactions()
        
        # Get payment entries with proper amount calculation based on payment type
        payment_entries = frappe.db.sql("""
            SELECT
                pe.name as voucher_no,
                pe.posting_date,
                'Payment Entry' as voucher_type,
                pe.mode_of_payment,
                pe.payment_type,
                CASE
                    WHEN pe.payment_type = 'Pay' THEN -pe.paid_amount
                    ELSE pe.paid_amount
                END as amount,
                0 as is_return
            FROM `tabPayment Entry` pe
            WHERE pe.owner = %s
                AND pe.docstatus = 1
                AND pe.creation BETWEEN %s AND %s
        """, (self.cashier, from_datetime, to_datetime), as_dict=1)
        
        # Get sales invoice payments including returns
        invoice_payments = frappe.db.sql("""
            SELECT
                si.name as voucher_no,
                si.posting_date,
                CASE
                    WHEN si.is_return = 1 THEN 'Sales Invoice Return'
                    ELSE 'Sales Invoice'
                END as voucher_type,
                sip.mode_of_payment,
                'Receive' as payment_type,
                CASE
                    WHEN si.is_return = 1 THEN -sip.amount
                    ELSE sip.amount
                END as amount,
                si.is_return
            FROM `tabSales Invoice` si
            JOIN `tabSales Invoice Payment` sip ON sip.parent = si.name
            WHERE si.owner = %s
                AND si.pos_profile = %s
                AND si.docstatus = 1
                AND si.status IN ('Paid', 'Return')
                AND si.creation BETWEEN %s AND %s
        """, (self.cashier, self.pos_profile, from_datetime, to_datetime), as_dict=1)
        
        # Filter out already processed transactions
        filtered_payment_entries = [
            entry for entry in payment_entries 
            if f"{entry.voucher_type}:{entry.voucher_no}" not in processed_transactions
        ]
        
        filtered_invoice_payments = [
            entry for entry in invoice_payments 
            if f"{entry.voucher_type}:{entry.voucher_no}" not in processed_transactions
        ]
        
        # Combine both results
        return filtered_payment_entries + filtered_invoice_payments

    def get_processed_transactions(self):
        """Get list of transactions already processed in other closing entries"""
        processed = set()
        
        # Get all closing entries for this cashier
        closing_entries = frappe.get_all(
            "Counter Closing Entry",
            filters={
                "cashier": self.cashier,
                "docstatus": 1,
                "name": ["!=", self.name or ""]
            },
            fields=["name"]
        )
        
        # Get all transactions from these closing entries
        for entry in closing_entries:
            transactions = frappe.get_all(
                "Cashier Closing Transaction",
                filters={"parent": entry.name},
                fields=["transaction_type", "transaction_id"]
            )
            
            for txn in transactions:
                processed.add(f"{txn.transaction_type}:{txn.transaction_id}")
        
        return processed
        
    def on_update(self):
        self.calculate_differences()
        self.calculate_totals()
        
    def calculate_differences(self):
        total_difference = 0
        for row in self.payment_reconciliation:
            row.difference = flt(row.closing_amount) - flt(row.expected_amount)
            total_difference += row.difference

        self.total_cash_difference = total_difference

    def calculate_totals(self):
        """Calculate grand total, net total, and transaction count"""
        grand_total = 0
        net_total = 0
        transaction_count = 0

        for transaction in self.transactions:
            transaction_count += 1
            amount = flt(transaction.amount)
            grand_total += abs(amount)  # Absolute value for grand total
            net_total += amount  # Actual amount (positive/negative) for net total

        self.grand_total = grand_total
        self.net_total = net_total
        self.total_transactions = transaction_count
