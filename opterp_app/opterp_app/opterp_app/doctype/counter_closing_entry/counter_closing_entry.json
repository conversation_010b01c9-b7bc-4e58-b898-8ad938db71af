{"actions": [], "allow_rename": 1, "autoname": "naming_series:", "creation": "2025-04-10 10:00:00", "doctype": "DocType", "engine": "InnoDB", "field_order": ["naming_series", "cashier", "company", "column_break_1", "counter_opening_entry", "pos_profile", "section_break_period", "period_start_date", "posting_date", "column_break_juqt", "period_end_date", "section_break_transactions", "transactions", "section_break_reconciliation", "payment_reconciliation", "section_break_summary", "grand_total", "net_total", "column_break_summary", "total_cash_difference", "total_transactions", "amended_from"], "fields": [{"default": "COUNTER-CLOSE-", "fieldname": "naming_series", "fieldtype": "Select", "label": "Series", "options": "COUNTER-CLOSE-", "reqd": 1}, {"fieldname": "cashier", "fieldtype": "Link", "in_list_view": 1, "label": "Cashier", "options": "User", "reqd": 1}, {"fieldname": "company", "fieldtype": "Link", "label": "Company", "options": "Company", "reqd": 1}, {"fieldname": "column_break_1", "fieldtype": "Column Break"}, {"fieldname": "counter_opening_entry", "fieldtype": "Link", "label": "Counter Opening Entry", "options": "Counter Opening Entry", "reqd": 1}, {"fieldname": "section_break_period", "fieldtype": "Section Break", "label": "Period"}, {"fetch_from": "counter_opening_entry.period_start_date", "fieldname": "period_start_date", "fieldtype": "Datetime", "label": "Period Start Date", "read_only": 1}, {"fieldname": "period_end_date", "fieldtype": "Datetime", "label": "Period End Date", "reqd": 1}, {"fieldname": "posting_date", "fieldtype": "Date", "label": "Posting Date", "reqd": 1}, {"fieldname": "section_break_transactions", "fieldtype": "Section Break", "label": "Transactions"}, {"fieldname": "transactions", "fieldtype": "Table", "label": "Transactions", "options": "Cashier Closing Transaction"}, {"fieldname": "section_break_reconciliation", "fieldtype": "Section Break", "label": "Payment Reconciliation"}, {"fieldname": "payment_reconciliation", "fieldtype": "Table", "label": "Payment Reconciliation", "options": "Cashier Closing Payment"}, {"fieldname": "section_break_summary", "fieldtype": "Section Break", "label": "Summary"}, {"fieldname": "grand_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Grand Total", "read_only": 1}, {"fieldname": "net_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Total", "read_only": 1}, {"fieldname": "column_break_summary", "fieldtype": "Column Break"}, {"fieldname": "total_cash_difference", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Cash Difference", "read_only": 1}, {"fieldname": "total_transactions", "fieldtype": "Int", "label": "Total Transactions", "read_only": 1}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "Counter Closing Entry", "print_hide": 1, "read_only": 1}, {"fieldname": "pos_profile", "fieldtype": "Link", "label": "POS Profile", "options": "POS Profile", "reqd": 1}, {"fieldname": "column_break_juqt", "fieldtype": "Column Break"}], "is_submittable": 1, "links": [], "modified": "2025-08-05 15:08:15.681048", "modified_by": "Administrator", "module": "Opterp App", "name": "Counter Closing Entry", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts Manager", "share": 1, "write": 1}, {"create": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts User", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}