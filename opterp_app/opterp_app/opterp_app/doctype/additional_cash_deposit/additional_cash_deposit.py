# Copyright (c) 2025, <PERSON><PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe import _
from frappe.model.document import Document
from frappe.utils import flt, now_datetime, getdate, nowtime


class AdditionalCashDeposit(Document):
    def validate(self):
        self.validate_counter_opening_entry()
        self.calculate_total_deposit()
        self.set_defaults()

    def on_submit(self):
        self.update_counter_opening_balance()

    def on_cancel(self):
        self.reverse_counter_opening_balance()

    def set_defaults(self):
        if not self.posting_date:
            self.posting_date = getdate()
        if not self.posting_time:
            self.posting_time = nowtime()

    def validate_counter_opening_entry(self):
        if not self.counter_opening_entry:
            frappe.throw(_("Counter Opening Entry is required"))

        # Get counter opening entry details
        opening_entry = frappe.get_doc("Counter Opening Entry", self.counter_opening_entry)
        
        # Validate that the counter is still open
        if opening_entry.status != "Open":
            frappe.throw(_("Cannot add cash deposit. Counter Opening Entry {0} is not in Open status").format(self.counter_opening_entry))

        # Set cashier and pos_profile from opening entry
        self.cashier = opening_entry.cashier
        self.pos_profile = opening_entry.pos_profile

        # Validate that current user is the same cashier or has permission
        if frappe.session.user != opening_entry.cashier and not frappe.has_permission("Counter Opening Entry", "write"):
            frappe.throw(_("Only the assigned cashier {0} can add cash deposits to this counter").format(opening_entry.cashier))

    def calculate_total_deposit(self):
        total = 0
        for detail in self.deposit_details:
            total += flt(detail.deposit_amount)
        self.total_deposit_amount = total

    def update_counter_opening_balance(self):
        """Update the opening balance in Counter Opening Entry"""
        opening_entry = frappe.get_doc("Counter Opening Entry", self.counter_opening_entry)
        
        # Update balance details with additional deposits
        for deposit_detail in self.deposit_details:
            mode_found = False
            for balance_detail in opening_entry.balance_details:
                if balance_detail.mode_of_payment == deposit_detail.mode_of_payment:
                    balance_detail.opening_amount += flt(deposit_detail.deposit_amount)
                    mode_found = True
                    break
            
            # If mode of payment not found, add new row
            if not mode_found:
                opening_entry.append("balance_details", {
                    "mode_of_payment": deposit_detail.mode_of_payment,
                    "opening_amount": flt(deposit_detail.deposit_amount)
                })
        
        # Save the opening entry (ignore permissions since this is system update)
        opening_entry.save(ignore_permissions=True)
        
        frappe.msgprint(_("Counter opening balance updated with additional deposit of {0}").format(
            frappe.format_value(self.total_deposit_amount, "Currency")
        ))

    def reverse_counter_opening_balance(self):
        """Reverse the balance update when cancelled"""
        opening_entry = frappe.get_doc("Counter Opening Entry", self.counter_opening_entry)
        
        # Reverse balance details
        for deposit_detail in self.deposit_details:
            for balance_detail in opening_entry.balance_details:
                if balance_detail.mode_of_payment == deposit_detail.mode_of_payment:
                    balance_detail.opening_amount -= flt(deposit_detail.deposit_amount)
                    # Remove row if amount becomes zero
                    if balance_detail.opening_amount <= 0:
                        opening_entry.remove(balance_detail)
                    break
        
        # Save the opening entry
        opening_entry.save(ignore_permissions=True)
        
        frappe.msgprint(_("Additional deposit of {0} has been reversed from counter opening balance").format(
            frappe.format_value(self.total_deposit_amount, "Currency")
        ))


@frappe.whitelist()
def get_counter_opening_details(counter_opening_entry):
    """Get details of counter opening entry for validation"""
    if not counter_opening_entry:
        return {}
    
    opening_entry = frappe.get_doc("Counter Opening Entry", counter_opening_entry)
    return {
        "cashier": opening_entry.cashier,
        "pos_profile": opening_entry.pos_profile,
        "status": opening_entry.status,
        "period_start_date": opening_entry.period_start_date
    }
