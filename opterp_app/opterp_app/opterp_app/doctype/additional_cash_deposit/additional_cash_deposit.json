{"actions": [], "allow_rename": 1, "autoname": "format:ADD-CASH-{counter_opening_entry}-{###}", "creation": "2025-08-05 16:30:00", "doctype": "DocType", "engine": "InnoDB", "field_order": ["counter_opening_entry", "cashier", "pos_profile", "column_break_1", "posting_date", "posting_time", "section_break_deposit", "deposit_details", "section_break_totals", "total_deposit_amount", "column_break_2", "remarks", "amended_from"], "fields": [{"fieldname": "counter_opening_entry", "fieldtype": "Link", "in_list_view": 1, "label": "Counter Opening Entry", "options": "Counter Opening Entry", "reqd": 1}, {"fieldname": "cashier", "fieldtype": "Link", "label": "Cashier", "options": "User", "read_only": 1}, {"fieldname": "pos_profile", "fieldtype": "Link", "label": "POS Profile", "options": "POS Profile", "read_only": 1}, {"fieldname": "column_break_1", "fieldtype": "Column Break"}, {"fieldname": "posting_date", "fieldtype": "Date", "label": "Posting Date", "reqd": 1}, {"fieldname": "posting_time", "fieldtype": "Time", "label": "Posting Time", "reqd": 1}, {"fieldname": "section_break_deposit", "fieldtype": "Section Break", "label": "Additional Deposit Details"}, {"fieldname": "deposit_details", "fieldtype": "Table", "label": "De<PERSON>sit Details", "options": "Additional Cash Deposit Detail"}, {"fieldname": "section_break_totals", "fieldtype": "Section Break", "label": "Totals"}, {"fieldname": "total_deposit_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Deposit Amount", "read_only": 1}, {"fieldname": "column_break_2", "fieldtype": "Column Break"}, {"fieldname": "remarks", "fieldtype": "Small Text", "label": "Remarks"}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "Additional Cash Deposit", "print_hide": 1, "read_only": 1}], "is_submittable": 1, "links": [], "modified": "2025-08-05 16:30:00.000000", "modified_by": "Administrator", "module": "Opterp App", "name": "Additional Cash Deposit", "naming_rule": "Expression", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "submit": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts Manager", "share": 1, "submit": 1, "write": 1}, {"create": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts User", "share": 1, "submit": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}