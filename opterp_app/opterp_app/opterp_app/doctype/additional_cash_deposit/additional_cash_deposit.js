// Copyright (c) 2025, <PERSON><PERSON><PERSON> and contributors
// For license information, please see license.txt

frappe.ui.form.on("Additional Cash Deposit", {
    refresh: function(frm) {
        if (frm.doc.docstatus === 0) {
            frm.add_custom_button(__("Calculate Total"), function() {
                frm.trigger("calculate_total_deposit");
            });
        }
        
        // Set default posting date and time
        if (!frm.doc.posting_date) {
            frm.set_value("posting_date", frappe.datetime.get_today());
        }
        if (!frm.doc.posting_time) {
            frm.set_value("posting_time", frappe.datetime.now_time());
        }
    },

    counter_opening_entry: function(frm) {
        if (frm.doc.counter_opening_entry) {
            frappe.call({
                method: "opterp_app.opterp_app.doctype.additional_cash_deposit.additional_cash_deposit.get_counter_opening_details",
                args: {
                    counter_opening_entry: frm.doc.counter_opening_entry
                },
                callback: function(r) {
                    if (r.message) {
                        frm.set_value("cashier", r.message.cashier);
                        frm.set_value("pos_profile", r.message.pos_profile);
                        
                        if (r.message.status !== "Open") {
                            frappe.msgprint({
                                title: __("Warning"),
                                indicator: "orange",
                                message: __("Counter Opening Entry {0} is not in Open status. Current status: {1}", 
                                    [frm.doc.counter_opening_entry, r.message.status])
                            });
                        }
                    }
                }
            });
        }
    },

    calculate_total_deposit: function(frm) {
        let total = 0;
        frm.doc.deposit_details.forEach(function(row) {
            total += flt(row.deposit_amount);
        });
        frm.set_value("total_deposit_amount", total);
    }
});

frappe.ui.form.on("Additional Cash Deposit Detail", {
    deposit_amount: function(frm, cdt, cdn) {
        frm.trigger("calculate_total_deposit");
    },
    
    deposit_details_remove: function(frm) {
        frm.trigger("calculate_total_deposit");
    }
});

// Set query for counter opening entry to show only open entries
frappe.ui.form.on("Additional Cash Deposit", {
    setup: function(frm) {
        frm.set_query("counter_opening_entry", function() {
            return {
                filters: {
                    "docstatus": 1,
                    "status": "Open"
                }
            };
        });
    }
});
