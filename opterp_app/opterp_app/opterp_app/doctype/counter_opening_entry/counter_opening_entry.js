// Copyright (c) 2025, <PERSON><PERSON><PERSON> and contributors
// For license information, please see license.txt

frappe.ui.form.on('Counter Opening Entry', {
    onload: function(frm) {
        if (!frm.doc.period_start_date) {
            frm.set_value('period_start_date', frappe.datetime.now_datetime());
        }
        
        if (!frm.doc.posting_date) {
            frm.set_value('posting_date', frappe.datetime.get_today());
        }
        
        if (!frm.doc.cashier) {
            frm.set_value('cashier', frappe.session.user);
        }
        
        if (!frm.doc.company) {
            frm.call('frappe.client.get_value', {
                doctype: 'Company',
                filters: {
                    'is_group': 0
                },
                fieldname: 'name'
            }).then(r => {
                if (r.message && r.message.name) {
                    frm.set_value('company', r.message.name);
                }
            });
        }
    },
    
    refresh: function(frm) {
        if (frm.doc.docstatus === 1 && frm.doc.status === 'Open') {
            // Add button for creating closing entry
            frm.add_custom_button(__('Create Closing Entry'), function() {
                frappe.model.open_mapped_doc({
                    method: 'opterp_app.opterp_app.doctype.counter_opening_entry.counter_opening_entry.make_closing_entry',
                    frm: frm
                });
            }).addClass('btn-primary');

            // Add button for additional cash deposit
            frm.add_custom_button(__('Add Cash Deposit'), function() {
                frappe.new_doc('Additional Cash Deposit', {
                    counter_opening_entry: frm.doc.name,
                    cashier: frm.doc.cashier,
                    pos_profile: frm.doc.pos_profile
                });
            }, __('Actions'));

            // Add button to view additional deposits
            if (frm.doc.additional_deposits_count > 0) {
                frm.add_custom_button(__('View Additional Deposits'), function() {
                    frappe.route_options = {
                        "counter_opening_entry": frm.doc.name
                    };
                    frappe.set_route("List", "Additional Cash Deposit");
                }, __('View'));
            }
        }

        // Update additional deposits summary
        if (frm.doc.docstatus === 1) {
            frm.trigger('update_additional_deposits_summary');
        }
    },

    update_additional_deposits_summary: function(frm) {
        if (frm.doc.docstatus === 1) {
            frappe.call({
                method: 'frappe.client.get_list',
                args: {
                    doctype: 'Additional Cash Deposit',
                    filters: {
                        counter_opening_entry: frm.doc.name,
                        docstatus: 1
                    },
                    fields: ['total_deposit_amount']
                },
                callback: function(r) {
                    if (r.message) {
                        let total = 0;
                        r.message.forEach(function(deposit) {
                            total += flt(deposit.total_deposit_amount);
                        });
                        frm.set_value('total_additional_deposits', total);
                        frm.set_value('additional_deposits_count', r.message.length);
                    }
                }
            });
        }
    }
});