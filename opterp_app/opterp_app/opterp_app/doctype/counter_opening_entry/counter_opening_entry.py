# Copyright (c) 2025, <PERSON><PERSON><PERSON> and contributors
# For license information, please see license.txt

import json
import frappe
from frappe import _
from frappe.model.document import Document
from frappe.utils import flt, now_datetime, getdate
from frappe.model.naming import make_autoname

class CounterOpeningEntry(Document):
    def validate(self):
        self.validate_duplicate_opening()
        self.set_status()
        self.check_handover_discrepancy()

    def on_submit(self):
        self.set_status()

    def on_cancel(self):
        self.set_status()

    def set_status(self):
        if self.docstatus == 2:
            self.status = "Cancelled"
        elif self.docstatus == 1:
            if self.counter_closing_entry:
                self.status = "Closed"
            else:
                self.status = "Open"
        else:
            self.status = "Draft"

    def validate_duplicate_opening(self):
        # Check if there's already an open entry for this cashier
        open_entry = frappe.db.exists(
            "Counter Opening Entry",
            {
                "cashier": self.cashier,
                "docstatus": 1,
                "status": "Open"
            }
        )

        if open_entry and open_entry != self.name:
            frappe.throw(f"Cashier {self.cashier} already has an open entry: {open_entry}")
        # Check if there's already an open entry for this POS Profile (Counter)
        open_counter = frappe.db.exists(
            "Counter Opening Entry",
            {
                "pos_profile": self.pos_profile,
                "docstatus": 1,
                "status": "Open",
                "name": ["!=", self.name or ""]
            }
        )

        if open_counter:
            counter_doc = frappe.get_doc("Counter Opening Entry", open_counter)
            frappe.throw(f"Counter {self.pos_profile} is already open by Cashier {counter_doc.cashier} (Entry: {open_counter})")

    def check_handover_discrepancy(self):
        """Check if there's a discrepancy between previous cashier's closing amount and current opening amount"""
        if self.docstatus == 1:  # Only check during draft/validation
            previous_closing = self.get_previous_closing_amount()
            current_opening = self.get_total_opening_amount()

            if (
                previous_closing and abs(previous_closing - current_opening) > 0.01
            ):  # Allow for minor rounding differences
                frappe.msgprint(
                    _(
                        "Warning: Previous cashier closed with {0}, but you're opening with {1}. Difference: {2}"
                    ).format(
                        frappe.format_value(previous_closing, "Currency"),
                        frappe.format_value(current_opening, "Currency"),
                        frappe.format_value(
                            current_opening - previous_closing, "Currency"
                        ),
                    ),
                    indicator="orange",
                    title=_("Handover Discrepancy"),
                )

    def get_previous_closing_amount(self):
        """Get the closing amount from the last cashier who closed this counter"""
        previous_closing = frappe.db.sql(
            """
            SELECT SUM(ccp.closing_amount) as total_closing
            FROM `tabCounter Closing Entry` cce
            JOIN `tabCashier Closing Payment` ccp ON ccp.parent = cce.name
            JOIN `tabCounter Opening Entry` coe ON coe.name = cce.counter_opening_entry
            WHERE coe.pos_profile = %s
                AND cce.docstatus = 1
                AND coe.period_start_date < %s
            ORDER BY coe.period_start_date DESC
            LIMIT 1
        """,
            (self.pos_profile, self.period_start_date),
        )

        return (
            previous_closing[0][0] if previous_closing and previous_closing[0][0] else 0
        )

    def get_total_opening_amount(self):
        """Get total opening amount for this entry"""
        total = 0
        for detail in self.balance_details:
            total += flt(detail.opening_amount)
        return total


@frappe.whitelist()
def make_closing_entry(source_name, target_doc=None):
    from frappe.model.mapper import get_mapped_doc

    def set_missing_values(source, target):
        target.naming_series = "COUNTER-CLOSE-"
        target.period_start_date = source.period_start_date
        target.period_end_date = now_datetime()
        target.posting_date = getdate()

    doc = get_mapped_doc("Counter Opening Entry", source_name, {
        "Counter Opening Entry": {
            "doctype": "Counter Closing Entry",
            "field_map": {
                "name": "counter_opening_entry",
                "cashier": "cashier",
                "company": "company"
            }
        }
    }, target_doc, set_missing_values)

    return doc


@frappe.whitelist()
def check_opening_entry(cashier):
    open_vouchers = frappe.db.get_all(
        "Counter Opening Entry",
        filters={"cashier": cashier, "counter_closing_entry": ["in", ["", None]], "docstatus": 1},
        fields=["name", "company", "pos_profile", "period_start_date"],
        order_by="period_start_date desc",
    )

    return open_vouchers


@frappe.whitelist()
def create_opening_entry(pos_profile, company, balance_details):
    balance_details = json.loads(balance_details)

    new_counter_opening = frappe.get_doc(
        {
            "doctype": "Counter Opening Entry",
            "period_start_date": frappe.utils.get_datetime(),
            "posting_date": frappe.utils.getdate(),
            "cashier": frappe.session.user,
            "pos_profile": pos_profile,
            "company": company,
        }
    )
    new_counter_opening.set("balance_details", balance_details)
    new_counter_opening.submit()

    return new_counter_opening.as_dict()
