{"actions": [], "allow_rename": 1, "autoname": "format:OPEN-{cashier}-{period_start_date}", "creation": "2025-04-10 10:00:00", "doctype": "DocType", "engine": "InnoDB", "field_order": ["cashier", "company", "pos_profile", "section_break_period", "period_start_date", "posting_date", "section_break_balance", "balance_details", "section_break_status", "status", "counter_closing_entry", "amended_from"], "fields": [{"fieldname": "cashier", "fieldtype": "Link", "in_list_view": 1, "label": "Cashier", "options": "User", "reqd": 1}, {"fieldname": "company", "fieldtype": "Link", "label": "Company", "options": "Company", "reqd": 1}, {"fieldname": "section_break_period", "fieldtype": "Section Break", "label": "Period"}, {"fieldname": "period_start_date", "fieldtype": "Datetime", "label": "Period Start Date", "reqd": 1}, {"fieldname": "posting_date", "fieldtype": "Date", "label": "Posting Date", "reqd": 1}, {"fieldname": "section_break_balance", "fieldtype": "Section Break", "label": "Opening Balance"}, {"fieldname": "balance_details", "fieldtype": "Table", "label": "Balance Details", "options": "Cashier Opening Balance Detail"}, {"fieldname": "section_break_status", "fieldtype": "Section Break"}, {"fieldname": "status", "fieldtype": "Select", "label": "Status", "options": "Draft\nOpen\nClosed\nCancelled", "read_only": 1}, {"fieldname": "counter_closing_entry", "fieldtype": "Data", "label": "Counter Closing Entry", "read_only": 1}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "Counter Opening Entry", "print_hide": 1, "read_only": 1}, {"fieldname": "pos_profile", "fieldtype": "Link", "label": "POS Profile", "options": "POS Profile", "reqd": 1}], "is_submittable": 1, "links": [], "modified": "2025-08-05 16:07:59.947946", "modified_by": "Administrator", "module": "Opterp App", "name": "Counter Opening Entry", "naming_rule": "Expression", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts Manager", "share": 1, "write": 1}, {"create": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts User", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}