// Copyright (c) 2025, <PERSON><PERSON><PERSON> and contributors
// For license information, please see license.txt

frappe.query_reports["Additional Cash Deposits Summary"] = {
    "filters": [
        {
            "fieldname": "from_date",
            "label": __("From Date"),
            "fieldtype": "Date",
            "default": frappe.datetime.add_months(frappe.datetime.get_today(), -1),
            "reqd": 1
        },
        {
            "fieldname": "to_date",
            "label": __("To Date"),
            "fieldtype": "Date",
            "default": frappe.datetime.get_today(),
            "reqd": 1
        },
        {
            "fieldname": "cashier",
            "label": __("Cashier"),
            "fieldtype": "Link",
            "options": "User"
        },
        {
            "fieldname": "pos_profile",
            "label": __("POS Profile"),
            "fieldtype": "Link",
            "options": "POS Profile"
        },
        {
            "fieldname": "counter_opening_entry",
            "label": __("Counter Opening Entry"),
            "fieldtype": "Link",
            "options": "Counter Opening Entry"
        }
    ]
};
