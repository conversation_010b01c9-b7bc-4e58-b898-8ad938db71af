# Copyright (c) 2025, <PERSON><PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe import _
from frappe.utils import flt, getdate


def execute(filters=None):
    columns = get_columns()
    data = get_data(filters)
    return columns, data


def get_columns():
    return [
        {
            "fieldname": "counter_opening_entry",
            "label": _("Counter Opening Entry"),
            "fieldtype": "Link",
            "options": "Counter Opening Entry",
            "width": 180
        },
        {
            "fieldname": "cashier",
            "label": _("Cashier"),
            "fieldtype": "Link",
            "options": "User",
            "width": 120
        },
        {
            "fieldname": "pos_profile",
            "label": _("POS Profile"),
            "fieldtype": "Link",
            "options": "POS Profile",
            "width": 120
        },
        {
            "fieldname": "deposit_name",
            "label": _("Deposit Entry"),
            "fieldtype": "Link",
            "options": "Additional Cash Deposit",
            "width": 150
        },
        {
            "fieldname": "posting_date",
            "label": _("Posting Date"),
            "fieldtype": "Date",
            "width": 100
        },
        {
            "fieldname": "posting_time",
            "label": _("Posting Time"),
            "fieldtype": "Time",
            "width": 100
        },
        {
            "fieldname": "mode_of_payment",
            "label": _("Mode of Payment"),
            "fieldtype": "Link",
            "options": "Mode of Payment",
            "width": 120
        },
        {
            "fieldname": "deposit_amount",
            "label": _("Deposit Amount"),
            "fieldtype": "Currency",
            "width": 120
        },
        {
            "fieldname": "total_deposit_amount",
            "label": _("Total Deposit"),
            "fieldtype": "Currency",
            "width": 120
        },
        {
            "fieldname": "remarks",
            "label": _("Remarks"),
            "fieldtype": "Data",
            "width": 200
        }
    ]


def get_data(filters):
    conditions = get_conditions(filters)
    
    data = frappe.db.sql(f"""
        SELECT 
            acd.counter_opening_entry,
            acd.cashier,
            acd.pos_profile,
            acd.name as deposit_name,
            acd.posting_date,
            acd.posting_time,
            acdd.mode_of_payment,
            acdd.deposit_amount,
            acd.total_deposit_amount,
            COALESCE(acdd.remarks, acd.remarks) as remarks
        FROM `tabAdditional Cash Deposit` acd
        LEFT JOIN `tabAdditional Cash Deposit Detail` acdd ON acdd.parent = acd.name
        WHERE acd.docstatus = 1 {conditions}
        ORDER BY acd.posting_date DESC, acd.posting_time DESC, acd.name
    """, as_dict=1)
    
    return data


def get_conditions(filters):
    conditions = ""
    
    if filters.get("from_date"):
        conditions += f" AND acd.posting_date >= '{filters.get('from_date')}'"
    
    if filters.get("to_date"):
        conditions += f" AND acd.posting_date <= '{filters.get('to_date')}'"
    
    if filters.get("cashier"):
        conditions += f" AND acd.cashier = '{filters.get('cashier')}'"
    
    if filters.get("pos_profile"):
        conditions += f" AND acd.pos_profile = '{filters.get('pos_profile')}'"
    
    if filters.get("counter_opening_entry"):
        conditions += f" AND acd.counter_opening_entry = '{filters.get('counter_opening_entry')}'"
    
    return conditions
