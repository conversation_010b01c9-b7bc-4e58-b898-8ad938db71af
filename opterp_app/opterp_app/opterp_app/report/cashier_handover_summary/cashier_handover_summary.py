# Copyright (c) 2025, <PERSON><PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe import _
from frappe.utils import flt, getdate


def execute(filters=None):
	columns = get_columns()
	data = get_data(filters)
	return columns, data


def get_columns():
	return [
		{
			"fieldname": "pos_profile",
			"label": _("Counter (POS Profile)"),
			"fieldtype": "Link",
			"options": "POS Profile",
			"width": 150
		},
		{
			"fieldname": "handover_date",
			"label": _("Handover Date"),
			"fieldtype": "Date",
			"width": 120
		},
		{
			"fieldname": "previous_cashier",
			"label": _("Previous Cashier"),
			"fieldtype": "Link",
			"options": "User",
			"width": 120
		},
		{
			"fieldname": "previous_closing_amount",
			"label": _("Previous Closing Amount"),
			"fieldtype": "Currency",
			"width": 150
		},
		{
			"fieldname": "next_cashier",
			"label": _("Next Cashier"),
			"fieldtype": "Link",
			"options": "User",
			"width": 120
		},
		{
			"fieldname": "next_opening_amount",
			"label": _("Next Opening Amount"),
			"fieldtype": "Currency",
			"width": 150
		},
		{
			"fieldname": "handover_difference",
			"label": _("Handover Difference"),
			"fieldtype": "Currency",
			"width": 150
		},
		{
			"fieldname": "previous_closing_entry",
			"label": _("Previous Closing Entry"),
			"fieldtype": "Link",
			"options": "Counter Closing Entry",
			"width": 150
		},
		{
			"fieldname": "next_opening_entry",
			"label": _("Next Opening Entry"),
			"fieldtype": "Link",
			"options": "Counter Opening Entry",
			"width": 150
		}
	]


def get_data(filters):
	conditions = get_conditions(filters)
	
	# Get handover data by finding consecutive opening/closing entries for the same counter
	data = frappe.db.sql(f"""
		WITH handovers AS (
			SELECT 
				coe1.pos_profile,
				DATE(coe1.period_start_date) as handover_date,
				cce_prev.cashier as previous_cashier,
				COALESCE(SUM(ccp_prev.closing_amount), 0) as previous_closing_amount,
				coe1.cashier as next_cashier,
				COALESCE(SUM(obd1.opening_amount), 0) as next_opening_amount,
				cce_prev.name as previous_closing_entry,
				coe1.name as next_opening_entry,
				ROW_NUMBER() OVER (PARTITION BY coe1.pos_profile ORDER BY coe1.period_start_date) as rn
			FROM `tabCounter Opening Entry` coe1
			LEFT JOIN `tabCashier Opening Balance Detail` obd1 ON obd1.parent = coe1.name
			LEFT JOIN `tabCounter Opening Entry` coe_prev ON coe_prev.pos_profile = coe1.pos_profile 
				AND coe_prev.period_start_date < coe1.period_start_date
				AND coe_prev.docstatus = 1
			LEFT JOIN `tabCounter Closing Entry` cce_prev ON cce_prev.counter_opening_entry = coe_prev.name
			LEFT JOIN `tabCashier Closing Payment` ccp_prev ON ccp_prev.parent = cce_prev.name
			WHERE coe1.docstatus = 1 
				AND coe_prev.name IS NOT NULL
				AND cce_prev.name IS NOT NULL
				{conditions}
			GROUP BY coe1.name, cce_prev.name
		)
		SELECT 
			pos_profile,
			handover_date,
			previous_cashier,
			previous_closing_amount,
			next_cashier,
			next_opening_amount,
			(next_opening_amount - previous_closing_amount) as handover_difference,
			previous_closing_entry,
			next_opening_entry
		FROM handovers
		WHERE rn > 1  -- Exclude first opening entry as it has no previous handover
		ORDER BY pos_profile, handover_date DESC
	""", filters, as_dict=1)
	
	return data


def get_conditions(filters):
	conditions = ""
	
	if filters.get("from_date"):
		conditions += " AND DATE(coe1.period_start_date) >= %(from_date)s"
	
	if filters.get("to_date"):
		conditions += " AND DATE(coe1.period_start_date) <= %(to_date)s"
	
	if filters.get("pos_profile"):
		conditions += " AND coe1.pos_profile = %(pos_profile)s"
	
	if filters.get("company"):
		conditions += " AND coe1.company = %(company)s"
	
	return conditions
