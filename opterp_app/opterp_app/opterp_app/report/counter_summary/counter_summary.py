# Copyright (c) 2025, <PERSON><PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe import _
from frappe.utils import flt, getdate


def execute(filters=None):
	columns = get_columns()
	data = get_data(filters)
	return columns, data


def get_columns():
	return [
		{
			"fieldname": "pos_profile",
			"label": _("Counter (POS Profile)"),
			"fieldtype": "Link",
			"options": "POS Profile",
			"width": 150
		},
		{
			"fieldname": "cashier",
			"label": _("Cashier"),
			"fieldtype": "Link",
			"options": "User",
			"width": 120
		},
		{
			"fieldname": "opening_entry",
			"label": _("Opening Entry"),
			"fieldtype": "Link",
			"options": "Counter Opening Entry",
			"width": 150
		},
		{
			"fieldname": "closing_entry",
			"label": _("Closing Entry"),
			"fieldtype": "Link",
			"options": "Counter Closing Entry",
			"width": 150
		},
		{
			"fieldname": "period_start",
			"label": _("Period Start"),
			"fieldtype": "Datetime",
			"width": 150
		},
		{
			"fieldname": "period_end",
			"label": _("Period End"),
			"fieldtype": "Datetime",
			"width": 150
		},
		{
			"fieldname": "opening_amount",
			"label": _("Opening Amount"),
			"fieldtype": "Currency",
			"width": 120
		},
		{
			"fieldname": "expected_amount",
			"label": _("Expected Amount"),
			"fieldtype": "Currency",
			"width": 120
		},
		{
			"fieldname": "closing_amount",
			"label": _("Closing Amount"),
			"fieldtype": "Currency",
			"width": 120
		},
		{
			"fieldname": "difference",
			"label": _("Difference"),
			"fieldtype": "Currency",
			"width": 100
		},
		{
			"fieldname": "grand_total",
			"label": _("Grand Total"),
			"fieldtype": "Currency",
			"width": 120
		},
		{
			"fieldname": "net_total",
			"label": _("Net Total"),
			"fieldtype": "Currency",
			"width": 120
		},
		{
			"fieldname": "total_transactions",
			"label": _("Total Transactions"),
			"fieldtype": "Int",
			"width": 120
		},
		{
			"fieldname": "status",
			"label": _("Status"),
			"fieldtype": "Data",
			"width": 100
		}
	]


def get_data(filters):
	conditions = get_conditions(filters)
	
	data = frappe.db.sql(f"""
		SELECT 
			coe.pos_profile,
			coe.cashier,
			coe.name as opening_entry,
			cce.name as closing_entry,
			coe.period_start_date as period_start,
			cce.period_end_date as period_end,
			COALESCE(SUM(obd.opening_amount), 0) as opening_amount,
			COALESCE(SUM(ccp.expected_amount), 0) as expected_amount,
			COALESCE(SUM(ccp.closing_amount), 0) as closing_amount,
			COALESCE(cce.total_cash_difference, 0) as difference,
			COALESCE(cce.grand_total, 0) as grand_total,
			COALESCE(cce.net_total, 0) as net_total,
			COALESCE(cce.total_transactions, 0) as total_transactions,
			coe.status
		FROM `tabCounter Opening Entry` coe
		LEFT JOIN `tabCounter Closing Entry` cce ON cce.counter_opening_entry = coe.name
		LEFT JOIN `tabCashier Opening Balance Detail` obd ON obd.parent = coe.name
		LEFT JOIN `tabCashier Closing Payment` ccp ON ccp.parent = cce.name
		WHERE coe.docstatus = 1 {conditions}
		GROUP BY coe.name, cce.name
		ORDER BY coe.period_start_date DESC
	""", filters, as_dict=1)
	
	return data


def get_conditions(filters):
	conditions = ""
	
	if filters.get("from_date"):
		conditions += " AND DATE(coe.period_start_date) >= %(from_date)s"
	
	if filters.get("to_date"):
		conditions += " AND DATE(coe.period_start_date) <= %(to_date)s"
	
	if filters.get("pos_profile"):
		conditions += " AND coe.pos_profile = %(pos_profile)s"
	
	if filters.get("cashier"):
		conditions += " AND coe.cashier = %(cashier)s"
	
	if filters.get("company"):
		conditions += " AND coe.company = %(company)s"
	
	return conditions
