// // Fix for get_payment_data missing invoice parameter error
// frappe.provide("erpnext.taxes_and_totals");

// // Override the problematic method in taxes_and_totals
// if (erpnext.taxes_and_totals) {
//     const originalSetPaymentModeOfPayments = erpnext.taxes_and_totals.prototype.set_payment_mode_of_payments;
    
//     erpnext.taxes_and_totals.prototype.set_payment_mode_of_payments = async function() {
//         // Handle non-return cases or cases without return_against
//         if (!this.frm.doc.is_return) {
//             return originalSetPaymentModeOfPayments?.call(this);
//         }

//         // For POS returns without return_against, skip payment mode copying
//         if (this.frm.doc.is_return && !this.frm.doc.return_against) {
//             console.log("POS return without return_against - using default payment modes");
//             return;
//         }

//         /*
//         During returns, if an user select mode of payment other than
//         default mode of payment, it should retain the user selection
//         instead resetting it to default mode of payment.
//         */

//         let payment_amount = 0;
//         this.frm.doc.payments.forEach(payment => {
//             payment_amount += payment.amount
//         });

//         let total_amount_to_pay = this.frm.doc.rounded_total || this.frm.doc.grand_total;

//         if (payment_amount == total_amount_to_pay) {
//             return;
//         }

//         /*
//         For partial return, if the payment was made using single mode of payment
//         it should set the return to that mode of payment only.
//         */

//         try {
//             let return_against_mop = await frappe.call({
//                 method: 'erpnext.controllers.sales_and_purchase_return.get_payment_data',
//                 args: {
//                     invoice: this.frm.doc.return_against
//                 }
//             });

//             if (return_against_mop && return_against_mop.message && return_against_mop.message.length === 1) {
//                 let mode_of_payment = return_against_mop.message[0].mode_of_payment;

//                 this.frm.doc.payments.forEach(payment => {
//                     if (payment.mode_of_payment === mode_of_payment) {
//                         payment.amount = total_amount_to_pay;
//                     } else {
//                         payment.amount = 0;
//                     }
//                 });

//                 this.frm.refresh_field("payments");
//             }
//         } catch (error) {
//             console.error("Error in set_payment_mode_of_payments:", error);
//             // For POS returns or other errors, just continue without copying payment modes
//             console.log("Continuing with default payment mode handling");
//         }
//     };
// }
