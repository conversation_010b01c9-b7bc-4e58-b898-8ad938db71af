// // Fix for POS returns without return_against invoice
// frappe.ui.form.on("Sales Invoice", {
//     is_return: function(frm) {
//         // Handle POS return setup
//         if (frm.doc.is_return && frm.doc.is_pos && !frm.doc.return_against) {
//             console.log("Setting up POS return without return_against");
            
//             // Ensure proper payment setup for POS returns
//             if (!frm.doc.payments || frm.doc.payments.length === 0) {
//                 // Add default payment mode if none exists
//                 frm.add_child("payments", {
//                     mode_of_payment: "Cash",
//                     amount: 0
//                 });
//                 frm.refresh_field("payments");
//             }
//         }
//     },
    
//     refresh: function(frm) {
//         // Additional handling for POS returns
//         if (frm.doc.is_return && frm.doc.is_pos && !frm.doc.return_against) {
//             // Override any problematic payment calculations
//             setTimeout(() => {
//                 if (frm.doc.payments) {
//                     frm.doc.payments.forEach(payment => {
//                         if (!payment.amount) {
//                             payment.amount = 0;
//                         }
//                     });
//                     frm.refresh_field("payments");
//                 }
//             }, 500);
//         }
//     }
// });

// // Override POS specific payment handling
// frappe.provide("erpnext.pos");

// // Ensure POS returns work without return_against
// $(document).ready(function() {
//     // Override any POS-specific payment calculations that might cause issues
//     if (window.erpnext && window.erpnext.taxes_and_totals) {
//         const originalCalculateTaxes = erpnext.taxes_and_totals.prototype.calculate_taxes;
        
//         erpnext.taxes_and_totals.prototype.calculate_taxes = function() {
//             try {
//                 // For POS returns without return_against, skip problematic payment mode calculations
//                 if (this.frm.doc.is_return && this.frm.doc.is_pos && !this.frm.doc.return_against) {
//                     // Call original but catch any errors
//                     return originalCalculateTaxes.call(this);
//                 } else {
//                     return originalCalculateTaxes.call(this);
//                 }
//             } catch (error) {
//                 console.error("Error in calculate_taxes for POS return:", error);
//                 // Continue with basic calculation
//                 this.calculate_totals();
//             }
//         };
//     }
// });
