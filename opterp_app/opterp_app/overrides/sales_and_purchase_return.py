# import frappe
# from frappe import _


# @frappe.whitelist()
# def get_payment_data(invoice=None):
#     if not invoice:
#         # For POS returns or when no invoice is specified, return empty payment data
#         # This allows the system to use default payment modes
#         return []

#     payment = frappe.db.get_all(
#         "Sales Invoice Payment",
#         {"parent": invoice},
#         ["mode_of_payment", "amount"]
#     )
#     return payment
