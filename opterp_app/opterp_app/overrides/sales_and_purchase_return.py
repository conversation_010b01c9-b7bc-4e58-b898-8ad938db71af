# Copyright (c) 2025, <PERSON><PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe import _


@frappe.whitelist()
def get_payment_data(invoice=None):
    """
    Override ERPNext's get_payment_data function to handle missing invoice parameter
    """
    if not invoice:
        frappe.throw(_("Invoice parameter is required"))
    
    payment = frappe.db.get_all(
        "Sales Invoice Payment", 
        {"parent": invoice}, 
        ["mode_of_payment", "amount"]
    )
    return payment
