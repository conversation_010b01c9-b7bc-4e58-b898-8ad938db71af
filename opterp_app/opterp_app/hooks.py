app_name = "opterp"
app_title = "OptERP"
app_publisher = "<PERSON>yu<PERSON>"
app_description = "An app for OptERP Customizations"
app_email = "<EMAIL>"
app_license = "mit"
app_home = "/app/home"
app_logo_url = "/assets/opterp_app/images/opterp-logo.svg"
# Apps
# ------------------

# required_apps = []

# Each item in the list will be shown as an app in the apps page
add_to_apps_screen = [
	{
		"name": app_name,
		"logo": app_logo_url,
		"title": app_title,
		"route": app_home,
		"has_permission": "erpnext.check_app_permission",
	}
]

# Includes in <head>
# ------------------

# include js, css files in header of desk.html
app_include_css = [
    "/assets/opterp_app/css/opterp_app.css",
    "/assets/opterp_app/css/twilight_theme.bundle.css",
    "/assets/opterp_app/node_modules/nepali-date-picker/src/nepaliDatePicker.css",
    # "/assets/opterp_app/node_modules/@sajanm/nepali-date-picker/dist/nepali.datepicker.v4.0.8.min.css",
    "/assets/opterp_app/css/nepali_calendar.css",
]
app_include_js = [
    # "/assets/opterp_app/js/custom_control_date.js",
    # "/assets/opterp_app/js/nepali_datepicker.js",
    # "/assets/opterp_app/js/theme_switcher.js",
    "opterp_app.bundle.js",
    "/assets/opterp_app/node_modules/nepali-date-picker/src/jquery.nepaliDatePicker.js",
    # "/assets/opterp_app/node_modules/@sajanm/nepali-date-picker/dist/nepali.datepicker.v4.0.8.min.js",
    "/assets/opterp_app/js/page/form_manager.js",
    "/assets/opterp_app/js/page/list_view.js",
    "/assets/opterp_app/js/report/purchase_order_analysis.js",
    "/assets/opterp_app/js/report/trend_reports.js",
    "/assets/opterp_app/js/report/items_to_order_and_receive.js",
    "/assets/opterp_app/js/report/monthly_attendance_sheet.js",
    # "/assets/opterp_app/js/sales_invoice_return_fix.js",
    # "/assets/opterp_app/js/pos_return_fix.js",

]

boot_session = "opterp_app.api.boot_session"

after_migrate = ["opterp_app.api.whitelabel_patch"]

fixtures = [
    {
        "dt": "Custom Field",
        "filters": [["Translation", "source_text", "like", "%ERPNext%"]],
    }
]

doc_events = {
    "Sales Invoice": {"on_submit": "opterp_app.cbms_integration.api.send_sales_invoice"},
}
# include js, css files in header of web template
web_include_css = "/assets/opterp_app/css/opterp_web.css"
# web_include_js = "/assets/opterp_app/js/opterp_app.js"

# include custom scss in every website theme (without file extension ".scss")
# website_theme_scss = "opterp_app/public/scss/website"

# include js, css files in header of web form
# webform_include_js = {"doctype": "public/js/doctype.js"}
# webform_include_css = {"doctype": "public/css/doctype.css"}

# include js in page
# page_js = {"page" : "public/js/file.js"}

# include js in doctype views
doctype_js = {"Location" : "public/js/location.js",
              "Salary Slip" : "public/js/salary_slip.js"
            }
# doctype_list_js = {"Item" : "public/js/item_list.js"}
# doctype_tree_js = {"doctype" : "public/js/doctype_tree.js"}
# doctype_calendar_js = {"doctype" : "public/js/doctype_calendar.js"}

# Svg Icons
# ------------------
# include app icons in desk
# app_include_icons = "opterp_app/public/icons.svg"

# Home Pages
# ----------

# application home page (will override Website Settings)
# home_page = "login"

# website user home page (by Role)
# role_home_page = {
# 	"Role": "home_page"
# }

# Generators
# ----------

# automatically create page for each record of this doctype
# website_generators = ["Web Page"]

# Jinja
# ----------

# add methods and filters to jinja environment
# jinja = {
# 	"methods": "opterp_app.utils.jinja_methods",
# 	"filters": "opterp_app.utils.jinja_filters"
# }

# Installation
# ------------

# before_install = "opterp_app.install.before_install"
after_install = "opterp_app.install.after_install"

# Uninstallation
# ------------

before_uninstall = "opterp_app.uninstall.before_uninstall"
# after_uninstall = "opterp_app.uninstall.after_uninstall"

# Integration Setup
# ------------------
# To set up dependencies/integrations with other apps
# Name of the app being installed is passed as an argument

# before_app_install = "opterp_app.utils.before_app_install"
# after_app_install = "opterp_app.utils.after_app_install"

# Integration Cleanup
# -------------------
# To clean up dependencies/integrations with other apps
# Name of the app being uninstalled is passed as an argument

# before_app_uninstall = "opterp_app.utils.before_app_uninstall"
# after_app_uninstall = "opterp_app.utils.after_app_uninstall"

# Desk Notifications
# ------------------
# See frappe.core.notifications.get_notification_config

# notification_config = "opterp_app.notifications.get_notification_config"

# Permissions
# -----------
# Permissions evaluated in scripted ways

# permission_query_conditions = {
# 	"Event": "frappe.desk.doctype.event.event.get_permission_query_conditions",
# }
#
# has_permission = {
# 	"Event": "frappe.desk.doctype.event.event.has_permission",
# }

# DocType Class
# ---------------
# Override standard doctype classes

# override_doctype_class = {
# 	"ToDo": "custom_app.overrides.CustomToDo"
# }

# Document Events
# ---------------
# Hook on document methods and events

# doc_events = {
# 	"*": {
# 		"on_update": "method",
# 		"on_cancel": "method",
# 		"on_trash": "method"
# 	}
# }
doc_events = {
    "Employee Checkin": {
        "validate": "opterp_app.opterp_app.utils.validate_employee_location"
    }
}

# Scheduled Tasks
# ---------------

# scheduler_events = {
# 	"all": [
# 		"opterp_app.tasks.all"
# 	],
# 	"daily": [
# 		"opterp_app.tasks.daily"
# 	],
# 	"hourly": [
# 		"opterp_app.tasks.hourly"
# 	],
# 	"weekly": [
# 		"opterp_app.tasks.weekly"
# 	],
# 	"monthly": [
# 		"opterp_app.tasks.monthly"
# 	],
# }
scheduler_events = {
    "daily": ["opterp_app.opterp_app.utils.update_last_sync_of_checkin"]
}
# Testing
# -------

# before_tests = "opterp_app.install.before_tests"

# Overriding Methods
# ------------------------------
#
override_whitelisted_methods = {
    "frappe.core.doctype.user.user.switch_theme": "opterp_app.overrides.opterp_theme.switch_theme",
    # "erpnext.controllers.sales_and_purchase_return.get_payment_data": "opterp_app.overrides.sales_and_purchase_return.get_payment_data",
}
#
# each overriding function accepts a `data` argument;
# generated from the base implementation of the doctype dashboard,
# along with any modifications made in other Frappe apps
# override_doctype_dashboards = {
# 	"Task": "opterp_app.task.get_dashboard_data"
# }

# exempt linked doctypes from being automatically cancelled
#
# auto_cancel_exempted_doctypes = ["Auto Repeat"]

# Ignore links to specified DocTypes when deleting documents
# -----------------------------------------------------------

# ignore_links_on_delete = ["Communication", "ToDo"]

# Request Events
# ----------------
# before_request = ["opterp_app.utils.before_request"]
# after_request = ["opterp_app.utils.after_request"]

# Job Events
# ----------
# before_job = ["opterp_app.utils.before_job"]
# after_job = ["opterp_app.utils.after_job"]

# User Data Protection
# --------------------

# user_data_fields = [
# 	{
# 		"doctype": "{doctype_1}",
# 		"filter_by": "{filter_by}",
# 		"redact_fields": ["{field_1}", "{field_2}"],
# 		"partial": 1,
# 	},
# 	{
# 		"doctype": "{doctype_2}",
# 		"filter_by": "{filter_by}",
# 		"partial": 1,
# 	},
# 	{
# 		"doctype": "{doctype_3}",
# 		"strict": False,
# 	},
# 	{
# 		"doctype": "{doctype_4}"
# 	}
# ]

# Authentication and authorization
# --------------------------------

# auth_hooks = [
# 	"opterp_app.auth.validate"
# ]

# Automatically update python controller files with type annotations for this app.
# export_python_type_annotations = True

# default_log_clearing_doctypes = {
# 	"Logging DocType Name": 30  # days to retain logs
# }
