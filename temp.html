<!DOCTYPE html>
<html>

<head>

</head>

<body>
    <div {% if print_settings.repeat_header_footer %} id="header-html" class="hidden-pdf" {% endif %}
        class="header-html">
        {% if letter_head and not no_letterhead %}
        <div>{{ letter_head }}</div>
        {% endif %}
    </div>

    {% set dob = frappe.db.get_value("Patient", doc.patient, "dob") %}
    {% set years = 0 %}
    {% set months = 0 %}
    {% set days = 0 %}
    {% if dob %}
    {% set now_date = frappe.utils.nowdate() %}
    {% if doc.ref_doctype=="Sales Invoice" and doc.docname %}
    {% set now_date = frappe.db.get_value("Sales Invoice", doc.docname, "posting_date") %}
    {% endif %}
    {% set diff = frappe.utils.date_diff(now_date, dob) %}
    {% set years = diff//365 %}
    {% set months = (diff - (years * 365))//30 %}
    {% set days = ( (diff - (years * 365)) - (months * 30) ) %}
    {% endif %}

    {% set sex = frappe.db.get_value("Patient", doc.patient, "sex") %}

    {# Get timing information from the diagnostic report data #}
    {% set ns_timing = namespace(sample_collected_time=None, report_generated_time=None, found_both=false) %}

    {% if doc.get("docname") %}
    {% set full_data_for_display = diagnostic_report_print(doc.name) %}

    {# Extract timing from the first available observation #}
    {% if full_data_for_display and full_data_for_display[0] and not ns_timing.found_both %}
    {% for data in full_data_for_display[0] %}
    {% if not ns_timing.found_both %}
    {% if not data.get("has_component") and data.get("observation") is mapping %}
    {% set obs = data.get("observation") %}
    {% if not ns_timing.sample_collected_time and obs.get("received_time") %}
    {% set ns_timing.sample_collected_time = obs.get("received_time") %}
    {% endif %}
    {% if not ns_timing.report_generated_time and obs.get("time_of_result") %}
    {% set ns_timing.report_generated_time = obs.get("time_of_result") %}
    {% endif %}
    {% elif data.get("has_component") and data[data.get("observation")] %}
    {% for comps in data[data.get("observation")] %}
    {% if comps is mapping and comps.get("observation") is mapping and not ns_timing.found_both %}
    {% set obs = comps.get("observation") %}
    {% if not ns_timing.sample_collected_time and obs.get("received_time") %}
    {% set ns_timing.sample_collected_time = obs.get("received_time") %}
    {% endif %}
    {% if not ns_timing.report_generated_time and obs.get("time_of_result") %}
    {% set ns_timing.report_generated_time = obs.get("time_of_result") %}
    {% endif %}
    {% endif %}
    {% if ns_timing.sample_collected_time and ns_timing.report_generated_time %}
    {% set ns_timing.found_both = true %}
    {% endif %}
    {% endfor %}
    {% endif %}
    {% if ns_timing.sample_collected_time and ns_timing.report_generated_time %}
    {% set ns_timing.found_both = true %}
    {% endif %}
    {% endif %}
    {% endfor %}
    {% endif %}
    {% endif %}
    <div class="patient-info-section content-section">
        <div class="col-xs-6 column-break">
            <div class="row data-field">
                <div class="col-xs-4"><label>Client Name:</label></div>
                <div class="col-xs-8 value"><b>{{ doc.patient_name.upper() }}</b></div>
            </div>
            <div class="row data-field">
                <div class="col-xs-4"><label>MR Number:</label></div>
                <div class="col-xs-8 value">{{ doc.patient }}</div>
            </div>
            <div class="row data-field">
                <div class="col-xs-4"><label>Age/Gender:</label></div>
                <div class="col-xs-8 value">{{ years }}Y {{ months }}M {{ days }}D / {{ sex }}</div>
            </div>
            {% if ns_timing.sample_collected_time %}
            <div class="row data-field">
                <div class="col-xs-4"><label>Sample Collected:</label></div>
                <div class="col-xs-8 value">{{ frappe.utils.format_datetime(ns_timing.sample_collected_time)[:-3] }}
                </div>
            </div>
            {% endif %}


        </div>

        <div class="col-xs-6 column-break">
            <div class="row data-field">
                <div class="col-xs-4"><label>Referred By:</label></div>
                <div class="col-xs-8 value">
                    {{ doc.practitioner_name.upper() if doc.practitioner_name else "Self" }}
                </div>
            </div>
            <div class="row data-field">
                <div class="col-xs-4"><label>Invoice No.:</label></div>
                <div class="col-xs-8 value">{{ doc.docname }}</div>
            </div>
            <div class="row data-field">
                <div class="col-xs-4"><label>Billed On:</label></div>
                <div class="col-xs-8 value">{{ frappe.utils.format_date(now_date) }}</div>
            </div>
            {% if ns_timing.report_generated_time %}
            <div class="row data-field">
                <div class="col-xs-4"><label>Report Generated:</label></div>
                <div class="col-xs-8 value">{{ frappe.utils.format_datetime(ns_timing.report_generated_time)[:-3] }}
                </div>
            </div>
            {% endif %}
        </div>
    </div>

    <hr class="content-section">

    <div class="observation-table-header">
        <div class="obs-field" style="width: 12%;">
            <div class="obs-sh-first-line"><b>SAMPLE</b></div>
        </div>
        <div class="obs-field" style="width: 28%;">
            <div class="obs-sh-first-line"><b>INVESTIGATION</b></div>
        </div>

        <div class="obs-field" style="width: 14%;">
            <div class="obs-sh-first-line"><b>RESULT</b></div>
        </div>
        <div class="obs-field" style="width: 10%;">
            <div class="obs-sh-first-line"><b>UNIT</b></div>
        </div>
        <div class="obs-field" style="width: 20%;">
            <div class="obs-sh-first-line"><b>REFERENCE INTERVAL</b></div>
        </div>
        <div class="obs-field" style="width: 16%;">
            <div class="obs-sh-first-line"><b>METHOD</b></div>
        </div>
    </div>

    {% if doc.get("docname") and full_data_for_display %}
    <div class="diagnostic-report content-section">
        {% for data in full_data_for_display[0] %}
        {# Handle non-component observations #}
        {% if not data.get("has_component") %}
        {% set obs = data.get("observation") %}
        {% if obs is mapping %}
        {% set observation_name = obs.get("preferred_display_name") or obs.get("observation_template") %}
        {% if obs.get("status") in ["Approved", "Draft"] and (obs.get("result_data") or obs.get("result_text") or
        obs.get("result_select")) not in [None, '', 'Null'] %}

        <div class="observation-section">
            <div class="group-title">
                <b>{{ observation_name }}</b>
            </div>

            <div class="observation single-obs">
                <div class="observation-details">
                    <div class="obs-field" style="width: 12%;">
                        <div class="obs-first-line">
                            {{ obs.get("sample") or frappe.db.get_value("Observation Template",
                            obs.get("observation_template"), "sample") or "" }}
                        </div>
                    </div>

                    <div class="obs-field" style="width: 28%;">
                        <div class="obs-first-line">
                            {{ observation_name }}
                        </div>
                    </div>



                    <div class="obs-field" style="width: 14%;">
                        <div class="obs-first-line text-center">
                            {% if obs.get("result_data") or obs.get("result_select") %}
                            {{ obs.get("result_data") or obs.get("result_select") }}
                            {% elif obs.get("result_text") %}
                            {% if '</div>' in obs.get("result_text") and obs.get("result_text")|length <= 60 %} {{
                            obs.get("result_text") }} {% elif obs.get("result_text")|length <=24 %} {{
                            obs.get("result_text") }} {% endif %} {% endif %} </div>
                    </div>

                    <div class="obs-field obs-first-line" style="width: 10%;">
                        {% if obs.get("permitted_unit") %}
                        {{ obs.get("permitted_unit") }}
                        {% else %}
                        {% set obs_template = obs.get("observation_template") %}
                        {% if obs_template %}
                        {{ frappe.db.get_value("Observation Template", obs_template, "uom") or "-" }}
                        {% else %}
                        -
                        {% endif %}
                        {% endif %}
                    </div>

                    <div class="obs-field ref-range" style="width: 20%;">
                        {# Check for custom_specific_rage first #}
                        {% if obs.get("custom_specific_rage") %}
                        <div>{{ obs.get("custom_specific_rage") }}</div>
                        {% else %}
                        {# Fall back to normal reference range logic #}
                        {% set template = frappe.get_doc("Observation Template", obs.get("observation_template")) %}
                        {% set ns = namespace(best_ref=None, best_priority=999) %}
                        {% for ref in template.observation_reference_range %}
                        {% if not (ref.normal_from == 0 and ref.normal_to == 0) %}
                        {% set applies_to = ref.applies_to | default("All") %}
                        {% set ref_age_type = ref.age | default("All") %}
                        {% set age_from = ref.age_from | int(0) %}
                        {% set age_to = ref.age_to | int(0) %}
                        {% set gender_match = (applies_to == "All" or applies_to.lower() == sex.lower()) %}
                        {% if ref_age_type == "All" %}
                        {% set age_match = true %}
                        {% elif ref_age_type == "Range" %}
                        {% set age_match = (years >= age_from and years <= age_to) %} {% else %} {% set age_match=false
                            %} {% endif %} {% if gender_match and age_match %} {% set priority=5 %} {# Default low
                            priority #} {% if applies_to !="All" and ref_age_type=="Range" %} {% set priority=1 %} {%
                            elif applies_to=="All" and ref_age_type=="Range" %} {% set priority=2 %} {% elif applies_to
                            !="All" and ref_age_type=="All" %} {% set priority=3 %} {% elif applies_to=="All" and
                            ref_age_type=="All" %} {% set priority=4 %} {% endif %} {% if ns.best_ref is none or
                            priority < ns.best_priority %} {% set ns.best_ref=ref %} {% set ns.best_priority=priority %}
                            {% endif %} {% endif %} {% endif %} {% endfor %} {% if ns.best_ref %} <div>{{
                            ns.best_ref.normal_from }} - {{ ns.best_ref.normal_to }}
                    </div>
                    {% else %}
                    <div class="no-reference">-</div>
                    {% endif %}
                    {% endif %}
                </div>
                <div class="obs-field" style="width: 16%;">
                    <div class="obs-first-line">
                        {{ obs.get("method") or "" }}
                    </div>
                </div>
            </div>

            {% if obs.get("result_text") and obs.get("result_text")|length > 24 %}
            <div class="note">{{ obs.get("result_text") }}</div>
            {% endif %}
            {% if obs.get("result_interpretation") %}
            <div class="note">{{ obs.get("result_interpretation") }}</div>
            {% endif %}
            {% if obs.get("note") %}
            <div class="note">{{ obs.get("note") }}</div>
            {% endif %}
            {% if obs.get("description") %}
            <div class="note">{{ obs.get("description") }}</div>
            {% endif %}
        </div>

    </div>

    {% endif %}
    {% endif %}
    {% endif %}

    {# Handle observations with components #}
    {% if data.get("has_component") and data[data.get("observation")] and data["has_result"] %}
    <div class="observation-section">
        <div class="group-title">
            <b>{{ data.get("display_name") }}</b>
        </div>
        <div class="grouped-obs">
            {% for comps in data[data.get("observation")] %}
            {% if comps is mapping %}
            {% set observation_object = comps.get("observation") %}
            {% if observation_object is mapping %}
            {% set observation_name = observation_object.get("preferred_display_name") or
            observation_object.get("observation_template") %}

            {% if observation_object.get("status") != 'Cancelled' and (observation_object.get("result_data") or
            observation_object.get("result_text") or observation_object.get("result_select") not in [None, "", "Null"])
            %}
            <div class="observation single-obs">
                <div class="observation-details">
                    <div class="obs-field" style="width: 12%;">
                        <div class="obs-first-line">
                            {{ observation_object.get("sample") or frappe.db.get_value("Observation Template",
                            observation_object.get("observation_template"), "sample") or "" }}
                        </div>
                    </div>

                    <div class="obs-field" style="width: 28%;">
                        <div class="obs-first-line">
                            {{ observation_name }}
                        </div>
                    </div>


                    <div class="obs-field" style="width: 14%;">
                        <div class="obs-first-line text-center">
                            {% if observation_object.get("result_data") or observation_object.get("result_select") %}
                            {{observation_object.get("result_data") or observation_object.get("result_select")}}
                            {% elif observation_object.get("result_text") %}
                            {% if '</div>' in observation_object.get("result_text") and
                        observation_object.get("result_text")|length <= 60 %} {{observation_object.get("result_text")}}
                            {% elif observation_object.get("result_text")|length <=24 %}
                            {{observation_object.get("result_text")}} {% endif %} {% endif %} </div>
                    </div>

                    <div class="obs-field obs-first-line" style="width: 10%;">
                        {% if observation_object.get("permitted_unit") %}
                        {{ observation_object.get("permitted_unit") }}
                        {% else %}
                        {% set obs_template = observation_object.get("observation_template") %}
                        {% if obs_template %}
                        {% set unit_from_template = frappe.db.get_value("Observation Template", obs_template, "uom") %}
                        {{ unit_from_template or "-" }}
                        {% else %}
                        -
                        {% endif %}
                        {% endif %}
                    </div>

                    <div class="obs-field ref-range" style="width: 20%;">
                        {# Check for custom_reference_rane first for component observations #}
                        {% if observation_object.get("custom_specific_rage") %}
                        <div>{{ observation_object.get("custom_specific_rage") }}</div>
                        {% else %}
                        {# Fall back to normal reference range logic for components #}
                        {% set current_sub_observation = observation_object %}
                        {% if current_sub_observation is mapping and current_sub_observation.get("observation_template")
                        %}
                        {% set template = frappe.get_doc("Observation Template",
                        current_sub_observation.get("observation_template")) %}

                        {% set ns = namespace(best_ref=None, best_priority=999) %}

                        {% for ref in template.observation_reference_range %}
                        {% if not (ref.normal_from == 0 and ref.normal_to == 0) %}

                        {% set applies_to = ref.applies_to | default("All") %}
                        {% set ref_age_type = ref.age | default("All") %}
                        {% set age_from = ref.age_from | int(0) %}
                        {% set age_to = ref.age_to | int(0) %}

                        {% set gender_match = (applies_to == "All" or applies_to.lower() == sex.lower()) %}

                        {% if ref_age_type == "All" %}
                        {% set age_match = true %}
                        {% elif ref_age_type == "Range" %}
                        {% set age_match = (years >= age_from and years <= age_to) %} {% else %} {% set age_match=false
                            %} {% endif %} {% if gender_match and age_match %} {% set priority=5 %} {# Default low
                            priority #} {% if applies_to !="All" and ref_age_type=="Range" %} {% set priority=1 %} {%
                            elif applies_to=="All" and ref_age_type=="Range" %} {% set priority=2 %} {% elif applies_to
                            !="All" and ref_age_type=="All" %} {% set priority=3 %} {% elif applies_to=="All" and
                            ref_age_type=="All" %} {% set priority=4 %} {% endif %} {% if ns.best_ref is none or
                            priority < ns.best_priority %} {% set ns.best_ref=ref %} {% set ns.best_priority=priority %}
                            {% endif %} {% endif %} {% endif %} {% endfor %} {% if ns.best_ref %} <div>
                            {{ ns.best_ref.normal_from }} - {{ ns.best_ref.normal_to }}
                    </div>
                    {% else %}
                    <div class="no-reference">
                        -
                    </div>
                    {% endif %}

                    {% else %}
                    <div class="no-reference">No reference found</div>
                    {% endif %}
                    {% endif %}


                </div>
                <div class="obs-field" style="width: 16%;">
                    {% if observation_object.get("method") %}
                    <div class="obs-first-line">
                        {{ observation_object.get("method") }}
                    </div>
                    {% endif %}
                </div>
            </div>

            {# Notes for component observation #}
            {% if observation_object.get("result_text") %}
            <div class="note">
                {{observation_object.get("result_text")}}
            </div>
            {% endif %}

            {% if observation_object.get("result_interpretation") %}
            <div class="note">
                {{observation_object.get("result_interpretation")}}
            </div>
            {% endif %}

            {% if observation_object.get("note") %}
            <div class="note">
                {{observation_object.get("note")}}
            </div>
            {% endif %}

            {% if observation_object.get("description") and not data.get("description") %}
            <div class="note">
                {{observation_object.get("description")}}
            </div>
            {% endif %}
        </div>
        {% endif %}
        {% endif %}
        {% endif %}
        {% endfor %} {# End of comps loop #}
        {% if data.get("description") %}
        <div class="note">
            {{data.get("description") or ""}}
        </div>
        {% endif %}
    </div>

    </div>
    {% endif %} {# End of has_component check #}
    {% endfor %} {# End of data in full_data_for_display[0] loop #}
    </div>
    {% endif %} {# End of doc.get("docname") and full_data_for_display check #}

    {% if doc.get("docname") and full_data_for_display %}
    {% for data in full_data_for_display[0] %}
    {% if data.get("has_component") %}
    {% set parent_observation_template = frappe.db.get_value("Observation", data.get("observation"),
    "observation_template") %}
    {% set template_doc = frappe.get_doc("Observation Template", parent_observation_template) if
    parent_observation_template else none %}

    {% if template_doc and template_doc.get("custom_component_description") %}
    <div class="component-description-section content-section">
        <h4>{{ data.get("display_name") }} - Additional Notes:</h4>
        <p>{{ template_doc.get("custom_component_description") }}</p>
    </div>
    {% endif %}
    {% endif %}
    {% endfor %}
    {% endif %}

    <div class="custom-note-section">
        {{ doc.custom_note or "" }}
    </div>

    <div class="main-footer">
        <div class="signature-section">
            <div class="signature-box">
                <div class="signature-line"></div>
                <div class="signature-label">Laboratory Technician</div>
                <div class="signature-sublabel">Name & Signature</div>
            </div>

            <div class="signature-box">
                <div class="signature-line"></div>
                <div class="signature-label">Pathologist</div>
                <div class="signature-sublabel">Name & Signature</div>
            </div>

            <div class="signature-box">
                <div class="signature-line"></div>
                <div class="signature-label">Medical Director</div>
                <div class="signature-sublabel">Name & Signature</div>
            </div>
        </div>

        {% if full_data_for_display and full_data_for_display[0] and full_data_for_display[0]|length > 0 %}
        <div class="end-of-report">
            <b>End of Report</b>
        </div>
        {% endif %}
    </div>

    </div>



</body>

</html><!DOCTYPE html>
<html>

<head>

</head>

<body>
    <div {% if print_settings.repeat_header_footer %} id="header-html" class="hidden-pdf" {% endif %}
        class="header-html">
        {% if letter_head and not no_letterhead %}
        <div>{{ letter_head }}</div>
        {% endif %}
    </div>

    {% set dob = frappe.db.get_value("Patient", doc.patient, "dob") %}
    {% set years = 0 %}
    {% set months = 0 %}
    {% set days = 0 %}
    {% if dob %}
    {% set now_date = frappe.utils.nowdate() %}
    {% if doc.ref_doctype=="Sales Invoice" and doc.docname %}
    {% set now_date = frappe.db.get_value("Sales Invoice", doc.docname, "posting_date") %}
    {% endif %}
    {% set diff = frappe.utils.date_diff(now_date, dob) %}
    {% set years = diff//365 %}
    {% set months = (diff - (years * 365))//30 %}
    {% set days = ( (diff - (years * 365)) - (months * 30) ) %}
    {% endif %}

    {% set sex = frappe.db.get_value("Patient", doc.patient, "sex") %}

    {# Get timing information from the diagnostic report data #}
    {% set ns_timing = namespace(sample_collected_time=None, report_generated_time=None, found_both=false) %}

    {% if doc.get("docname") %}
    {% set full_data_for_display = diagnostic_report_print(doc.name) %}

    {# Extract timing from the first available observation #}
    {% if full_data_for_display and full_data_for_display[0] and not ns_timing.found_both %}
    {% for data in full_data_for_display[0] %}
    {% if not ns_timing.found_both %}
    {% if not data.get("has_component") and data.get("observation") is mapping %}
    {% set obs = data.get("observation") %}
    {% if not ns_timing.sample_collected_time and obs.get("received_time") %}
    {% set ns_timing.sample_collected_time = obs.get("received_time") %}
    {% endif %}
    {% if not ns_timing.report_generated_time and obs.get("time_of_result") %}
    {% set ns_timing.report_generated_time = obs.get("time_of_result") %}
    {% endif %}
    {% elif data.get("has_component") and data[data.get("observation")] %}
    {% for comps in data[data.get("observation")] %}
    {% if comps is mapping and comps.get("observation") is mapping and not ns_timing.found_both %}
    {% set obs = comps.get("observation") %}
    {% if not ns_timing.sample_collected_time and obs.get("received_time") %}
    {% set ns_timing.sample_collected_time = obs.get("received_time") %}
    {% endif %}
    {% if not ns_timing.report_generated_time and obs.get("time_of_result") %}
    {% set ns_timing.report_generated_time = obs.get("time_of_result") %}
    {% endif %}
    {% endif %}
    {% if ns_timing.sample_collected_time and ns_timing.report_generated_time %}
    {% set ns_timing.found_both = true %}
    {% endif %}
    {% endfor %}
    {% endif %}
    {% if ns_timing.sample_collected_time and ns_timing.report_generated_time %}
    {% set ns_timing.found_both = true %}
    {% endif %}
    {% endif %}
    {% endfor %}
    {% endif %}
    {% endif %}
    <div class="patient-info-section content-section">
        <div class="col-xs-6 column-break">
            <div class="row data-field">
                <div class="col-xs-4"><label>Client Name:</label></div>
                <div class="col-xs-8 value"><b>{{ doc.patient_name.upper() }}</b></div>
            </div>
            <div class="row data-field">
                <div class="col-xs-4"><label>MR Number:</label></div>
                <div class="col-xs-8 value">{{ doc.patient }}</div>
            </div>
            <div class="row data-field">
                <div class="col-xs-4"><label>Age/Gender:</label></div>
                <div class="col-xs-8 value">{{ years }}Y {{ months }}M {{ days }}D / {{ sex }}</div>
            </div>
            {% if ns_timing.sample_collected_time %}
            <div class="row data-field">
                <div class="col-xs-4"><label>Sample Collected:</label></div>
                <div class="col-xs-8 value">{{ frappe.utils.format_datetime(ns_timing.sample_collected_time)[:-3] }}
                </div>
            </div>
            {% endif %}


        </div>

        <div class="col-xs-6 column-break">
            <div class="row data-field">
                <div class="col-xs-4"><label>Referred By:</label></div>
                <div class="col-xs-8 value">
                    {{ doc.practitioner_name.upper() if doc.practitioner_name else "Self" }}
                </div>
            </div>
            <div class="row data-field">
                <div class="col-xs-4"><label>Invoice No.:</label></div>
                <div class="col-xs-8 value">{{ doc.docname }}</div>
            </div>
            <div class="row data-field">
                <div class="col-xs-4"><label>Billed On:</label></div>
                <div class="col-xs-8 value">{{ frappe.utils.format_date(now_date) }}</div>
            </div>
            {% if ns_timing.report_generated_time %}
            <div class="row data-field">
                <div class="col-xs-4"><label>Report Generated:</label></div>
                <div class="col-xs-8 value">{{ frappe.utils.format_datetime(ns_timing.report_generated_time)[:-3] }}
                </div>
            </div>
            {% endif %}
        </div>
    </div>

    <hr class="content-section">

    <div class="observation-table-header">
        <div class="obs-field" style="width: 12%;">
            <div class="obs-sh-first-line"><b>SAMPLE</b></div>
        </div>
        <div class="obs-field" style="width: 28%;">
            <div class="obs-sh-first-line"><b>INVESTIGATION</b></div>
        </div>

        <div class="obs-field" style="width: 14%;">
            <div class="obs-sh-first-line"><b>RESULT</b></div>
        </div>
        <div class="obs-field" style="width: 10%;">
            <div class="obs-sh-first-line"><b>UNIT</b></div>
        </div>
        <div class="obs-field" style="width: 20%;">
            <div class="obs-sh-first-line"><b>REFERENCE INTERVAL</b></div>
        </div>
        <div class="obs-field" style="width: 16%;">
            <div class="obs-sh-first-line"><b>METHOD</b></div>
        </div>
    </div>

    {% if doc.get("docname") and full_data_for_display %}
    <div class="diagnostic-report content-section">
        {% for data in full_data_for_display[0] %}
        {# Handle non-component observations #}
        {% if not data.get("has_component") %}
        {% set obs = data.get("observation") %}
        {% if obs is mapping %}
        {% set observation_name = obs.get("preferred_display_name") or obs.get("observation_template") %}
        {% if obs.get("status") in ["Approved", "Draft"] and (obs.get("result_data") or obs.get("result_text") or
        obs.get("result_select")) not in [None, '', 'Null'] %}

        <div class="observation-section">
            <div class="group-title">
                <b>{{ observation_name }}</b>
            </div>

            <div class="observation single-obs">
                <div class="observation-details">
                    <div class="obs-field" style="width: 12%;">
                        <div class="obs-first-line">
                            {{ obs.get("sample") or frappe.db.get_value("Observation Template",
                            obs.get("observation_template"), "sample") or "" }}
                        </div>
                    </div>

                    <div class="obs-field" style="width: 28%;">
                        <div class="obs-first-line">
                            {{ observation_name }}
                        </div>
                    </div>



                    <div class="obs-field" style="width: 14%;">
                        <div class="obs-first-line text-center">
                            {% if obs.get("result_data") or obs.get("result_select") %}
                            {{ obs.get("result_data") or obs.get("result_select") }}
                            {% elif obs.get("result_text") %}
                            {% if '</div>' in obs.get("result_text") and obs.get("result_text")|length <= 60 %} {{
                            obs.get("result_text") }} {% elif obs.get("result_text")|length <=24 %} {{
                            obs.get("result_text") }} {% endif %} {% endif %} </div>
                    </div>

                    <div class="obs-field obs-first-line" style="width: 10%;">
                        {% if obs.get("permitted_unit") %}
                        {{ obs.get("permitted_unit") }}
                        {% else %}
                        {% set obs_template = obs.get("observation_template") %}
                        {% if obs_template %}
                        {{ frappe.db.get_value("Observation Template", obs_template, "uom") or "-" }}
                        {% else %}
                        -
                        {% endif %}
                        {% endif %}
                    </div>

                    <div class="obs-field ref-range" style="width: 20%;">
                        {# Check for custom_specific_rage first #}
                        {% if obs.get("custom_specific_rage") %}
                        <div>{{ obs.get("custom_specific_rage") }}</div>
                        {% else %}
                        {# Fall back to normal reference range logic #}
                        {% set template = frappe.get_doc("Observation Template", obs.get("observation_template")) %}
                        {% set ns = namespace(best_ref=None, best_priority=999) %}
                        {% for ref in template.observation_reference_range %}
                        {% if not (ref.normal_from == 0 and ref.normal_to == 0) %}
                        {% set applies_to = ref.applies_to | default("All") %}
                        {% set ref_age_type = ref.age | default("All") %}
                        {% set age_from = ref.age_from | int(0) %}
                        {% set age_to = ref.age_to | int(0) %}
                        {% set gender_match = (applies_to == "All" or applies_to.lower() == sex.lower()) %}
                        {% if ref_age_type == "All" %}
                        {% set age_match = true %}
                        {% elif ref_age_type == "Range" %}
                        {% set age_match = (years >= age_from and years <= age_to) %} {% else %} {% set age_match=false
                            %} {% endif %} {% if gender_match and age_match %} {% set priority=5 %} {# Default low
                            priority #} {% if applies_to !="All" and ref_age_type=="Range" %} {% set priority=1 %} {%
                            elif applies_to=="All" and ref_age_type=="Range" %} {% set priority=2 %} {% elif applies_to
                            !="All" and ref_age_type=="All" %} {% set priority=3 %} {% elif applies_to=="All" and
                            ref_age_type=="All" %} {% set priority=4 %} {% endif %} {% if ns.best_ref is none or
                            priority < ns.best_priority %} {% set ns.best_ref=ref %} {% set ns.best_priority=priority %}
                            {% endif %} {% endif %} {% endif %} {% endfor %} {% if ns.best_ref %} <div>{{
                            ns.best_ref.normal_from }} - {{ ns.best_ref.normal_to }}
                    </div>
                    {% else %}
                    <div class="no-reference">-</div>
                    {% endif %}
                    {% endif %}
                </div>
                <div class="obs-field" style="width: 16%;">
                    <div class="obs-first-line">
                        {{ obs.get("method") or "" }}
                    </div>
                </div>
            </div>

            {% if obs.get("result_text") and obs.get("result_text")|length > 24 %}
            <div class="note">{{ obs.get("result_text") }}</div>
            {% endif %}
            {% if obs.get("result_interpretation") %}
            <div class="note">{{ obs.get("result_interpretation") }}</div>
            {% endif %}
            {% if obs.get("note") %}
            <div class="note">{{ obs.get("note") }}</div>
            {% endif %}
            {% if obs.get("description") %}
            <div class="note">{{ obs.get("description") }}</div>
            {% endif %}
        </div>

    </div>

    {% endif %}
    {% endif %}
    {% endif %}

    {# Handle observations with components #}
    {% if data.get("has_component") and data[data.get("observation")] and data["has_result"] %}
    <div class="observation-section">
        <div class="group-title">
            <b>{{ data.get("display_name") }}</b>
        </div>
        <div class="grouped-obs">
            {% for comps in data[data.get("observation")] %}
            {% if comps is mapping %}
            {% set observation_object = comps.get("observation") %}
            {% if observation_object is mapping %}
            {% set observation_name = observation_object.get("preferred_display_name") or
            observation_object.get("observation_template") %}

            {% if observation_object.get("status") != 'Cancelled' and (observation_object.get("result_data") or
            observation_object.get("result_text") or observation_object.get("result_select") not in [None, "", "Null"])
            %}
            <div class="observation single-obs">
                <div class="observation-details">
                    <div class="obs-field" style="width: 12%;">
                        <div class="obs-first-line">
                            {{ observation_object.get("sample") or frappe.db.get_value("Observation Template",
                            observation_object.get("observation_template"), "sample") or "" }}
                        </div>
                    </div>

                    <div class="obs-field" style="width: 28%;">
                        <div class="obs-first-line">
                            {{ observation_name }}
                        </div>
                    </div>


                    <div class="obs-field" style="width: 14%;">
                        <div class="obs-first-line text-center">
                            {% if observation_object.get("result_data") or observation_object.get("result_select") %}
                            {{observation_object.get("result_data") or observation_object.get("result_select")}}
                            {% elif observation_object.get("result_text") %}
                            {% if '</div>' in observation_object.get("result_text") and
                        observation_object.get("result_text")|length <= 60 %} {{observation_object.get("result_text")}}
                            {% elif observation_object.get("result_text")|length <=24 %}
                            {{observation_object.get("result_text")}} {% endif %} {% endif %} </div>
                    </div>

                    <div class="obs-field obs-first-line" style="width: 10%;">
                        {% if observation_object.get("permitted_unit") %}
                        {{ observation_object.get("permitted_unit") }}
                        {% else %}
                        {% set obs_template = observation_object.get("observation_template") %}
                        {% if obs_template %}
                        {% set unit_from_template = frappe.db.get_value("Observation Template", obs_template, "uom") %}
                        {{ unit_from_template or "-" }}
                        {% else %}
                        -
                        {% endif %}
                        {% endif %}
                    </div>

                    <div class="obs-field ref-range" style="width: 20%;">
                        {# Check for custom_reference_rane first for component observations #}
                        {% if observation_object.get("custom_specific_rage") %}
                        <div>{{ observation_object.get("custom_specific_rage") }}</div>
                        {% else %}
                        {# Fall back to normal reference range logic for components #}
                        {% set current_sub_observation = observation_object %}
                        {% if current_sub_observation is mapping and current_sub_observation.get("observation_template")
                        %}
                        {% set template = frappe.get_doc("Observation Template",
                        current_sub_observation.get("observation_template")) %}

                        {% set ns = namespace(best_ref=None, best_priority=999) %}

                        {% for ref in template.observation_reference_range %}
                        {% if not (ref.normal_from == 0 and ref.normal_to == 0) %}

                        {% set applies_to = ref.applies_to | default("All") %}
                        {% set ref_age_type = ref.age | default("All") %}
                        {% set age_from = ref.age_from | int(0) %}
                        {% set age_to = ref.age_to | int(0) %}

                        {% set gender_match = (applies_to == "All" or applies_to.lower() == sex.lower()) %}

                        {% if ref_age_type == "All" %}
                        {% set age_match = true %}
                        {% elif ref_age_type == "Range" %}
                        {% set age_match = (years >= age_from and years <= age_to) %} {% else %} {% set age_match=false
                            %} {% endif %} {% if gender_match and age_match %} {% set priority=5 %} {# Default low
                            priority #} {% if applies_to !="All" and ref_age_type=="Range" %} {% set priority=1 %} {%
                            elif applies_to=="All" and ref_age_type=="Range" %} {% set priority=2 %} {% elif applies_to
                            !="All" and ref_age_type=="All" %} {% set priority=3 %} {% elif applies_to=="All" and
                            ref_age_type=="All" %} {% set priority=4 %} {% endif %} {% if ns.best_ref is none or
                            priority < ns.best_priority %} {% set ns.best_ref=ref %} {% set ns.best_priority=priority %}
                            {% endif %} {% endif %} {% endif %} {% endfor %} {% if ns.best_ref %} <div>
                            {{ ns.best_ref.normal_from }} - {{ ns.best_ref.normal_to }}
                    </div>
                    {% else %}
                    <div class="no-reference">
                        -
                    </div>
                    {% endif %}

                    {% else %}
                    <div class="no-reference">No reference found</div>
                    {% endif %}
                    {% endif %}


                </div>
                <div class="obs-field" style="width: 16%;">
                    {% if observation_object.get("method") %}
                    <div class="obs-first-line">
                        {{ observation_object.get("method") }}
                    </div>
                    {% endif %}
                </div>
            </div>

            {# Notes for component observation #}
            {% if observation_object.get("result_text") %}
            <div class="note">
                {{observation_object.get("result_text")}}
            </div>
            {% endif %}

            {% if observation_object.get("result_interpretation") %}
            <div class="note">
                {{observation_object.get("result_interpretation")}}
            </div>
            {% endif %}

            {% if observation_object.get("note") %}
            <div class="note">
                {{observation_object.get("note")}}
            </div>
            {% endif %}

            {% if observation_object.get("description") and not data.get("description") %}
            <div class="note">
                {{observation_object.get("description")}}
            </div>
            {% endif %}
        </div>
        {% endif %}
        {% endif %}
        {% endif %}
        {% endfor %} {# End of comps loop #}
        {% if data.get("description") %}
        <div class="note">
            {{data.get("description") or ""}}
        </div>
        {% endif %}
    </div>

    </div>
    {% endif %} {# End of has_component check #}
    {% endfor %} {# End of data in full_data_for_display[0] loop #}
    </div>
    {% endif %} {# End of doc.get("docname") and full_data_for_display check #}

    {% if doc.get("docname") and full_data_for_display %}
    {% for data in full_data_for_display[0] %}
    {% if data.get("has_component") %}
    {% set parent_observation_template = frappe.db.get_value("Observation", data.get("observation"),
    "observation_template") %}
    {% set template_doc = frappe.get_doc("Observation Template", parent_observation_template) if
    parent_observation_template else none %}

    {% if template_doc and template_doc.get("custom_component_description") %}
    <div class="component-description-section content-section">
        <h4>{{ data.get("display_name") }} - Additional Notes:</h4>
        <p>{{ template_doc.get("custom_component_description") }}</p>
    </div>
    {% endif %}
    {% endif %}
    {% endfor %}
    {% endif %}

    <div class="custom-note-section">
        {{ doc.custom_note or "" }}
    </div>

    <div class="main-footer">
        <div class="signature-section">
            <div class="signature-box">
                <div class="signature-line"></div>
                <div class="signature-label">Laboratory Technician</div>
                <div class="signature-sublabel">Name & Signature</div>
            </div>

            <div class="signature-box">
                <div class="signature-line"></div>
                <div class="signature-label">Pathologist</div>
                <div class="signature-sublabel">Name & Signature</div>
            </div>

            <div class="signature-box">
                <div class="signature-line"></div>
                <div class="signature-label">Medical Director</div>
                <div class="signature-sublabel">Name & Signature</div>
            </div>
        </div>

        {% if full_data_for_display and full_data_for_display[0] and full_data_for_display[0]|length > 0 %}
        <div class="end-of-report">
            <b>End of Report</b>
        </div>
        {% endif %}
    </div>

    </div>



</body>

</html><!DOCTYPE html>
<html>

<head>

</head>

<body>
    <div {% if print_settings.repeat_header_footer %} id="header-html" class="hidden-pdf" {% endif %}
        class="header-html">
        {% if letter_head and not no_letterhead %}
        <div>{{ letter_head }}</div>
        {% endif %}
    </div>

    {% set dob = frappe.db.get_value("Patient", doc.patient, "dob") %}
    {% set years = 0 %}
    {% set months = 0 %}
    {% set days = 0 %}
    {% if dob %}
    {% set now_date = frappe.utils.nowdate() %}
    {% if doc.ref_doctype=="Sales Invoice" and doc.docname %}
    {% set now_date = frappe.db.get_value("Sales Invoice", doc.docname, "posting_date") %}
    {% endif %}
    {% set diff = frappe.utils.date_diff(now_date, dob) %}
    {% set years = diff//365 %}
    {% set months = (diff - (years * 365))//30 %}
    {% set days = ( (diff - (years * 365)) - (months * 30) ) %}
    {% endif %}

    {% set sex = frappe.db.get_value("Patient", doc.patient, "sex") %}

    {# Get timing information from the diagnostic report data #}
    {% set ns_timing = namespace(sample_collected_time=None, report_generated_time=None, found_both=false) %}

    {% if doc.get("docname") %}
    {% set full_data_for_display = diagnostic_report_print(doc.name) %}

    {# Extract timing from the first available observation #}
    {% if full_data_for_display and full_data_for_display[0] and not ns_timing.found_both %}
    {% for data in full_data_for_display[0] %}
    {% if not ns_timing.found_both %}
    {% if not data.get("has_component") and data.get("observation") is mapping %}
    {% set obs = data.get("observation") %}
    {% if not ns_timing.sample_collected_time and obs.get("received_time") %}
    {% set ns_timing.sample_collected_time = obs.get("received_time") %}
    {% endif %}
    {% if not ns_timing.report_generated_time and obs.get("time_of_result") %}
    {% set ns_timing.report_generated_time = obs.get("time_of_result") %}
    {% endif %}
    {% elif data.get("has_component") and data[data.get("observation")] %}
    {% for comps in data[data.get("observation")] %}
    {% if comps is mapping and comps.get("observation") is mapping and not ns_timing.found_both %}
    {% set obs = comps.get("observation") %}
    {% if not ns_timing.sample_collected_time and obs.get("received_time") %}
    {% set ns_timing.sample_collected_time = obs.get("received_time") %}
    {% endif %}
    {% if not ns_timing.report_generated_time and obs.get("time_of_result") %}
    {% set ns_timing.report_generated_time = obs.get("time_of_result") %}
    {% endif %}
    {% endif %}
    {% if ns_timing.sample_collected_time and ns_timing.report_generated_time %}
    {% set ns_timing.found_both = true %}
    {% endif %}
    {% endfor %}
    {% endif %}
    {% if ns_timing.sample_collected_time and ns_timing.report_generated_time %}
    {% set ns_timing.found_both = true %}
    {% endif %}
    {% endif %}
    {% endfor %}
    {% endif %}
    {% endif %}
    <div class="patient-info-section content-section">
        <div class="col-xs-6 column-break">
            <div class="row data-field">
                <div class="col-xs-4"><label>Client Name:</label></div>
                <div class="col-xs-8 value"><b>{{ doc.patient_name.upper() }}</b></div>
            </div>
            <div class="row data-field">
                <div class="col-xs-4"><label>MR Number:</label></div>
                <div class="col-xs-8 value">{{ doc.patient }}</div>
            </div>
            <div class="row data-field">
                <div class="col-xs-4"><label>Age/Gender:</label></div>
                <div class="col-xs-8 value">{{ years }}Y {{ months }}M {{ days }}D / {{ sex }}</div>
            </div>
            {% if ns_timing.sample_collected_time %}
            <div class="row data-field">
                <div class="col-xs-4"><label>Sample Collected:</label></div>
                <div class="col-xs-8 value">{{ frappe.utils.format_datetime(ns_timing.sample_collected_time)[:-3] }}
                </div>
            </div>
            {% endif %}


        </div>

        <div class="col-xs-6 column-break">
            <div class="row data-field">
                <div class="col-xs-4"><label>Referred By:</label></div>
                <div class="col-xs-8 value">
                    {{ doc.practitioner_name.upper() if doc.practitioner_name else "Self" }}
                </div>
            </div>
            <div class="row data-field">
                <div class="col-xs-4"><label>Invoice No.:</label></div>
                <div class="col-xs-8 value">{{ doc.docname }}</div>
            </div>
            <div class="row data-field">
                <div class="col-xs-4"><label>Billed On:</label></div>
                <div class="col-xs-8 value">{{ frappe.utils.format_date(now_date) }}</div>
            </div>
            {% if ns_timing.report_generated_time %}
            <div class="row data-field">
                <div class="col-xs-4"><label>Report Generated:</label></div>
                <div class="col-xs-8 value">{{ frappe.utils.format_datetime(ns_timing.report_generated_time)[:-3] }}
                </div>
            </div>
            {% endif %}
        </div>
    </div>

    <hr class="content-section">

    <div class="observation-table-header">
        <div class="obs-field" style="width: 12%;">
            <div class="obs-sh-first-line"><b>SAMPLE</b></div>
        </div>
        <div class="obs-field" style="width: 28%;">
            <div class="obs-sh-first-line"><b>INVESTIGATION</b></div>
        </div>

        <div class="obs-field" style="width: 14%;">
            <div class="obs-sh-first-line"><b>RESULT</b></div>
        </div>
        <div class="obs-field" style="width: 10%;">
            <div class="obs-sh-first-line"><b>UNIT</b></div>
        </div>
        <div class="obs-field" style="width: 20%;">
            <div class="obs-sh-first-line"><b>REFERENCE INTERVAL</b></div>
        </div>
        <div class="obs-field" style="width: 16%;">
            <div class="obs-sh-first-line"><b>METHOD</b></div>
        </div>
    </div>

    {% if doc.get("docname") and full_data_for_display %}
    <div class="diagnostic-report content-section">
        {% for data in full_data_for_display[0] %}
        {# Handle non-component observations #}
        {% if not data.get("has_component") %}
        {% set obs = data.get("observation") %}
        {% if obs is mapping %}
        {% set observation_name = obs.get("preferred_display_name") or obs.get("observation_template") %}
        {% if obs.get("status") in ["Approved", "Draft"] and (obs.get("result_data") or obs.get("result_text") or
        obs.get("result_select")) not in [None, '', 'Null'] %}

        <div class="observation-section">
            <div class="group-title">
                <b>{{ observation_name }}</b>
            </div>

            <div class="observation single-obs">
                <div class="observation-details">
                    <div class="obs-field" style="width: 12%;">
                        <div class="obs-first-line">
                            {{ obs.get("sample") or frappe.db.get_value("Observation Template",
                            obs.get("observation_template"), "sample") or "" }}
                        </div>
                    </div>

                    <div class="obs-field" style="width: 28%;">
                        <div class="obs-first-line">
                            {{ observation_name }}
                        </div>
                    </div>



                    <div class="obs-field" style="width: 14%;">
                        <div class="obs-first-line text-center">
                            {% if obs.get("result_data") or obs.get("result_select") %}
                            {{ obs.get("result_data") or obs.get("result_select") }}
                            {% elif obs.get("result_text") %}
                            {% if '</div>' in obs.get("result_text") and obs.get("result_text")|length <= 60 %} {{
                            obs.get("result_text") }} {% elif obs.get("result_text")|length <=24 %} {{
                            obs.get("result_text") }} {% endif %} {% endif %} </div>
                    </div>

                    <div class="obs-field obs-first-line" style="width: 10%;">
                        {% if obs.get("permitted_unit") %}
                        {{ obs.get("permitted_unit") }}
                        {% else %}
                        {% set obs_template = obs.get("observation_template") %}
                        {% if obs_template %}
                        {{ frappe.db.get_value("Observation Template", obs_template, "uom") or "-" }}
                        {% else %}
                        -
                        {% endif %}
                        {% endif %}
                    </div>

                    <div class="obs-field ref-range" style="width: 20%;">
                        {# Check for custom_specific_rage first #}
                        {% if obs.get("custom_specific_rage") %}
                        <div>{{ obs.get("custom_specific_rage") }}</div>
                        {% else %}
                        {# Fall back to normal reference range logic #}
                        {% set template = frappe.get_doc("Observation Template", obs.get("observation_template")) %}
                        {% set ns = namespace(best_ref=None, best_priority=999) %}
                        {% for ref in template.observation_reference_range %}
                        {% if not (ref.normal_from == 0 and ref.normal_to == 0) %}
                        {% set applies_to = ref.applies_to | default("All") %}
                        {% set ref_age_type = ref.age | default("All") %}
                        {% set age_from = ref.age_from | int(0) %}
                        {% set age_to = ref.age_to | int(0) %}
                        {% set gender_match = (applies_to == "All" or applies_to.lower() == sex.lower()) %}
                        {% if ref_age_type == "All" %}
                        {% set age_match = true %}
                        {% elif ref_age_type == "Range" %}
                        {% set age_match = (years >= age_from and years <= age_to) %} {% else %} {% set age_match=false
                            %} {% endif %} {% if gender_match and age_match %} {% set priority=5 %} {# Default low
                            priority #} {% if applies_to !="All" and ref_age_type=="Range" %} {% set priority=1 %} {%
                            elif applies_to=="All" and ref_age_type=="Range" %} {% set priority=2 %} {% elif applies_to
                            !="All" and ref_age_type=="All" %} {% set priority=3 %} {% elif applies_to=="All" and
                            ref_age_type=="All" %} {% set priority=4 %} {% endif %} {% if ns.best_ref is none or
                            priority < ns.best_priority %} {% set ns.best_ref=ref %} {% set ns.best_priority=priority %}
                            {% endif %} {% endif %} {% endif %} {% endfor %} {% if ns.best_ref %} <div>{{
                            ns.best_ref.normal_from }} - {{ ns.best_ref.normal_to }}
                    </div>
                    {% else %}
                    <div class="no-reference">-</div>
                    {% endif %}
                    {% endif %}
                </div>
                <div class="obs-field" style="width: 16%;">
                    <div class="obs-first-line">
                        {{ obs.get("method") or "" }}
                    </div>
                </div>
            </div>

            {% if obs.get("result_text") and obs.get("result_text")|length > 24 %}
            <div class="note">{{ obs.get("result_text") }}</div>
            {% endif %}
            {% if obs.get("result_interpretation") %}
            <div class="note">{{ obs.get("result_interpretation") }}</div>
            {% endif %}
            {% if obs.get("note") %}
            <div class="note">{{ obs.get("note") }}</div>
            {% endif %}
            {% if obs.get("description") %}
            <div class="note">{{ obs.get("description") }}</div>
            {% endif %}
        </div>

    </div>

    {% endif %}
    {% endif %}
    {% endif %}

    {# Handle observations with components #}
    {% if data.get("has_component") and data[data.get("observation")] and data["has_result"] %}
    <div class="observation-section">
        <div class="group-title">
            <b>{{ data.get("display_name") }}</b>
        </div>
        <div class="grouped-obs">
            {% for comps in data[data.get("observation")] %}
            {% if comps is mapping %}
            {% set observation_object = comps.get("observation") %}
            {% if observation_object is mapping %}
            {% set observation_name = observation_object.get("preferred_display_name") or
            observation_object.get("observation_template") %}

            {% if observation_object.get("status") != 'Cancelled' and (observation_object.get("result_data") or
            observation_object.get("result_text") or observation_object.get("result_select") not in [None, "", "Null"])
            %}
            <div class="observation single-obs">
                <div class="observation-details">
                    <div class="obs-field" style="width: 12%;">
                        <div class="obs-first-line">
                            {{ observation_object.get("sample") or frappe.db.get_value("Observation Template",
                            observation_object.get("observation_template"), "sample") or "" }}
                        </div>
                    </div>

                    <div class="obs-field" style="width: 28%;">
                        <div class="obs-first-line">
                            {{ observation_name }}
                        </div>
                    </div>


                    <div class="obs-field" style="width: 14%;">
                        <div class="obs-first-line text-center">
                            {% if observation_object.get("result_data") or observation_object.get("result_select") %}
                            {{observation_object.get("result_data") or observation_object.get("result_select")}}
                            {% elif observation_object.get("result_text") %}
                            {% if '</div>' in observation_object.get("result_text") and
                        observation_object.get("result_text")|length <= 60 %} {{observation_object.get("result_text")}}
                            {% elif observation_object.get("result_text")|length <=24 %}
                            {{observation_object.get("result_text")}} {% endif %} {% endif %} </div>
                    </div>

                    <div class="obs-field obs-first-line" style="width: 10%;">
                        {% if observation_object.get("permitted_unit") %}
                        {{ observation_object.get("permitted_unit") }}
                        {% else %}
                        {% set obs_template = observation_object.get("observation_template") %}
                        {% if obs_template %}
                        {% set unit_from_template = frappe.db.get_value("Observation Template", obs_template, "uom") %}
                        {{ unit_from_template or "-" }}
                        {% else %}
                        -
                        {% endif %}
                        {% endif %}
                    </div>

                    <div class="obs-field ref-range" style="width: 20%;">
                        {# Check for custom_reference_rane first for component observations #}
                        {% if observation_object.get("custom_specific_rage") %}
                        <div>{{ observation_object.get("custom_specific_rage") }}</div>
                        {% else %}
                        {# Fall back to normal reference range logic for components #}
                        {% set current_sub_observation = observation_object %}
                        {% if current_sub_observation is mapping and current_sub_observation.get("observation_template")
                        %}
                        {% set template = frappe.get_doc("Observation Template",
                        current_sub_observation.get("observation_template")) %}

                        {% set ns = namespace(best_ref=None, best_priority=999) %}

                        {% for ref in template.observation_reference_range %}
                        {% if not (ref.normal_from == 0 and ref.normal_to == 0) %}

                        {% set applies_to = ref.applies_to | default("All") %}
                        {% set ref_age_type = ref.age | default("All") %}
                        {% set age_from = ref.age_from | int(0) %}
                        {% set age_to = ref.age_to | int(0) %}

                        {% set gender_match = (applies_to == "All" or applies_to.lower() == sex.lower()) %}

                        {% if ref_age_type == "All" %}
                        {% set age_match = true %}
                        {% elif ref_age_type == "Range" %}
                        {% set age_match = (years >= age_from and years <= age_to) %} {% else %} {% set age_match=false
                            %} {% endif %} {% if gender_match and age_match %} {% set priority=5 %} {# Default low
                            priority #} {% if applies_to !="All" and ref_age_type=="Range" %} {% set priority=1 %} {%
                            elif applies_to=="All" and ref_age_type=="Range" %} {% set priority=2 %} {% elif applies_to
                            !="All" and ref_age_type=="All" %} {% set priority=3 %} {% elif applies_to=="All" and
                            ref_age_type=="All" %} {% set priority=4 %} {% endif %} {% if ns.best_ref is none or
                            priority < ns.best_priority %} {% set ns.best_ref=ref %} {% set ns.best_priority=priority %}
                            {% endif %} {% endif %} {% endif %} {% endfor %} {% if ns.best_ref %} <div>
                            {{ ns.best_ref.normal_from }} - {{ ns.best_ref.normal_to }}
                    </div>
                    {% else %}
                    <div class="no-reference">
                        -
                    </div>
                    {% endif %}

                    {% else %}
                    <div class="no-reference">No reference found</div>
                    {% endif %}
                    {% endif %}


                </div>
                <div class="obs-field" style="width: 16%;">
                    {% if observation_object.get("method") %}
                    <div class="obs-first-line">
                        {{ observation_object.get("method") }}
                    </div>
                    {% endif %}
                </div>
            </div>

            {# Notes for component observation #}
            {% if observation_object.get("result_text") %}
            <div class="note">
                {{observation_object.get("result_text")}}
            </div>
            {% endif %}

            {% if observation_object.get("result_interpretation") %}
            <div class="note">
                {{observation_object.get("result_interpretation")}}
            </div>
            {% endif %}

            {% if observation_object.get("note") %}
            <div class="note">
                {{observation_object.get("note")}}
            </div>
            {% endif %}

            {% if observation_object.get("description") and not data.get("description") %}
            <div class="note">
                {{observation_object.get("description")}}
            </div>
            {% endif %}
        </div>
        {% endif %}
        {% endif %}
        {% endif %}
        {% endfor %} {# End of comps loop #}
        {% if data.get("description") %}
        <div class="note">
            {{data.get("description") or ""}}
        </div>
        {% endif %}
    </div>

    </div>
    {% endif %} {# End of has_component check #}
    {% endfor %} {# End of data in full_data_for_display[0] loop #}
    </div>
    {% endif %} {# End of doc.get("docname") and full_data_for_display check #}

    {% if doc.get("docname") and full_data_for_display %}
    {% for data in full_data_for_display[0] %}
    {% if data.get("has_component") %}
    {% set parent_observation_template = frappe.db.get_value("Observation", data.get("observation"),
    "observation_template") %}
    {% set template_doc = frappe.get_doc("Observation Template", parent_observation_template) if
    parent_observation_template else none %}

    {% if template_doc and template_doc.get("custom_component_description") %}
    <div class="component-description-section content-section">
        <h4>{{ data.get("display_name") }} - Additional Notes:</h4>
        <p>{{ template_doc.get("custom_component_description") }}</p>
    </div>
    {% endif %}
    {% endif %}
    {% endfor %}
    {% endif %}

    <div class="custom-note-section">
        {{ doc.custom_note or "" }}
    </div>

    <div class="main-footer">
        <div class="signature-section">
            <div class="signature-box">
                <div class="signature-line"></div>
                <div class="signature-label">Laboratory Technician</div>
                <div class="signature-sublabel">Name & Signature</div>
            </div>

            <div class="signature-box">
                <div class="signature-line"></div>
                <div class="signature-label">Pathologist</div>
                <div class="signature-sublabel">Name & Signature</div>
            </div>

            <div class="signature-box">
                <div class="signature-line"></div>
                <div class="signature-label">Medical Director</div>
                <div class="signature-sublabel">Name & Signature</div>
            </div>
        </div>

        {% if full_data_for_display and full_data_for_display[0] and full_data_for_display[0]|length > 0 %}
        <div class="end-of-report">
            <b>End of Report</b>
        </div>
        {% endif %}
    </div>

    </div>



</body>

</html><!DOCTYPE html>
<html>

<head>

</head>

<body>
    <div {% if print_settings.repeat_header_footer %} id="header-html" class="hidden-pdf" {% endif %}
        class="header-html">
        {% if letter_head and not no_letterhead %}
        <div>{{ letter_head }}</div>
        {% endif %}
    </div>

    {% set dob = frappe.db.get_value("Patient", doc.patient, "dob") %}
    {% set years = 0 %}
    {% set months = 0 %}
    {% set days = 0 %}
    {% if dob %}
    {% set now_date = frappe.utils.nowdate() %}
    {% if doc.ref_doctype=="Sales Invoice" and doc.docname %}
    {% set now_date = frappe.db.get_value("Sales Invoice", doc.docname, "posting_date") %}
    {% endif %}
    {% set diff = frappe.utils.date_diff(now_date, dob) %}
    {% set years = diff//365 %}
    {% set months = (diff - (years * 365))//30 %}
    {% set days = ( (diff - (years * 365)) - (months * 30) ) %}
    {% endif %}

    {% set sex = frappe.db.get_value("Patient", doc.patient, "sex") %}

    {# Get timing information from the diagnostic report data #}
    {% set ns_timing = namespace(sample_collected_time=None, report_generated_time=None, found_both=false) %}

    {% if doc.get("docname") %}
    {% set full_data_for_display = diagnostic_report_print(doc.name) %}

    {# Extract timing from the first available observation #}
    {% if full_data_for_display and full_data_for_display[0] and not ns_timing.found_both %}
    {% for data in full_data_for_display[0] %}
    {% if not ns_timing.found_both %}
    {% if not data.get("has_component") and data.get("observation") is mapping %}
    {% set obs = data.get("observation") %}
    {% if not ns_timing.sample_collected_time and obs.get("received_time") %}
    {% set ns_timing.sample_collected_time = obs.get("received_time") %}
    {% endif %}
    {% if not ns_timing.report_generated_time and obs.get("time_of_result") %}
    {% set ns_timing.report_generated_time = obs.get("time_of_result") %}
    {% endif %}
    {% elif data.get("has_component") and data[data.get("observation")] %}
    {% for comps in data[data.get("observation")] %}
    {% if comps is mapping and comps.get("observation") is mapping and not ns_timing.found_both %}
    {% set obs = comps.get("observation") %}
    {% if not ns_timing.sample_collected_time and obs.get("received_time") %}
    {% set ns_timing.sample_collected_time = obs.get("received_time") %}
    {% endif %}
    {% if not ns_timing.report_generated_time and obs.get("time_of_result") %}
    {% set ns_timing.report_generated_time = obs.get("time_of_result") %}
    {% endif %}
    {% endif %}
    {% if ns_timing.sample_collected_time and ns_timing.report_generated_time %}
    {% set ns_timing.found_both = true %}
    {% endif %}
    {% endfor %}
    {% endif %}
    {% if ns_timing.sample_collected_time and ns_timing.report_generated_time %}
    {% set ns_timing.found_both = true %}
    {% endif %}
    {% endif %}
    {% endfor %}
    {% endif %}
    {% endif %}
    <div class="patient-info-section content-section">
        <div class="col-xs-6 column-break">
            <div class="row data-field">
                <div class="col-xs-4"><label>Client Name:</label></div>
                <div class="col-xs-8 value"><b>{{ doc.patient_name.upper() }}</b></div>
            </div>
            <div class="row data-field">
                <div class="col-xs-4"><label>MR Number:</label></div>
                <div class="col-xs-8 value">{{ doc.patient }}</div>
            </div>
            <div class="row data-field">
                <div class="col-xs-4"><label>Age/Gender:</label></div>
                <div class="col-xs-8 value">{{ years }}Y {{ months }}M {{ days }}D / {{ sex }}</div>
            </div>
            {% if ns_timing.sample_collected_time %}
            <div class="row data-field">
                <div class="col-xs-4"><label>Sample Collected:</label></div>
                <div class="col-xs-8 value">{{ frappe.utils.format_datetime(ns_timing.sample_collected_time)[:-3] }}
                </div>
            </div>
            {% endif %}


        </div>

        <div class="col-xs-6 column-break">
            <div class="row data-field">
                <div class="col-xs-4"><label>Referred By:</label></div>
                <div class="col-xs-8 value">
                    {{ doc.practitioner_name.upper() if doc.practitioner_name else "Self" }}
                </div>
            </div>
            <div class="row data-field">
                <div class="col-xs-4"><label>Invoice No.:</label></div>
                <div class="col-xs-8 value">{{ doc.docname }}</div>
            </div>
            <div class="row data-field">
                <div class="col-xs-4"><label>Billed On:</label></div>
                <div class="col-xs-8 value">{{ frappe.utils.format_date(now_date) }}</div>
            </div>
            {% if ns_timing.report_generated_time %}
            <div class="row data-field">
                <div class="col-xs-4"><label>Report Generated:</label></div>
                <div class="col-xs-8 value">{{ frappe.utils.format_datetime(ns_timing.report_generated_time)[:-3] }}
                </div>
            </div>
            {% endif %}
        </div>
    </div>

    <hr class="content-section">

    <div class="observation-table-header">
        <div class="obs-field" style="width: 12%;">
            <div class="obs-sh-first-line"><b>SAMPLE</b></div>
        </div>
        <div class="obs-field" style="width: 28%;">
            <div class="obs-sh-first-line"><b>INVESTIGATION</b></div>
        </div>

        <div class="obs-field" style="width: 14%;">
            <div class="obs-sh-first-line"><b>RESULT</b></div>
        </div>
        <div class="obs-field" style="width: 10%;">
            <div class="obs-sh-first-line"><b>UNIT</b></div>
        </div>
        <div class="obs-field" style="width: 20%;">
            <div class="obs-sh-first-line"><b>REFERENCE INTERVAL</b></div>
        </div>
        <div class="obs-field" style="width: 16%;">
            <div class="obs-sh-first-line"><b>METHOD</b></div>
        </div>
    </div>

    {% if doc.get("docname") and full_data_for_display %}
    <div class="diagnostic-report content-section">
        {% for data in full_data_for_display[0] %}
        {# Handle non-component observations #}
        {% if not data.get("has_component") %}
        {% set obs = data.get("observation") %}
        {% if obs is mapping %}
        {% set observation_name = obs.get("preferred_display_name") or obs.get("observation_template") %}
        {% if obs.get("status") in ["Approved", "Draft"] and (obs.get("result_data") or obs.get("result_text") or
        obs.get("result_select")) not in [None, '', 'Null'] %}

        <div class="observation-section">
            <div class="group-title">
                <b>{{ observation_name }}</b>
            </div>

            <div class="observation single-obs">
                <div class="observation-details">
                    <div class="obs-field" style="width: 12%;">
                        <div class="obs-first-line">
                            {{ obs.get("sample") or frappe.db.get_value("Observation Template",
                            obs.get("observation_template"), "sample") or "" }}
                        </div>
                    </div>

                    <div class="obs-field" style="width: 28%;">
                        <div class="obs-first-line">
                            {{ observation_name }}
                        </div>
                    </div>



                    <div class="obs-field" style="width: 14%;">
                        <div class="obs-first-line text-center">
                            {% if obs.get("result_data") or obs.get("result_select") %}
                            {{ obs.get("result_data") or obs.get("result_select") }}
                            {% elif obs.get("result_text") %}
                            {% if '</div>' in obs.get("result_text") and obs.get("result_text")|length <= 60 %} {{
                            obs.get("result_text") }} {% elif obs.get("result_text")|length <=24 %} {{
                            obs.get("result_text") }} {% endif %} {% endif %} </div>
                    </div>

                    <div class="obs-field obs-first-line" style="width: 10%;">
                        {% if obs.get("permitted_unit") %}
                        {{ obs.get("permitted_unit") }}
                        {% else %}
                        {% set obs_template = obs.get("observation_template") %}
                        {% if obs_template %}
                        {{ frappe.db.get_value("Observation Template", obs_template, "uom") or "-" }}
                        {% else %}
                        -
                        {% endif %}
                        {% endif %}
                    </div>

                    <div class="obs-field ref-range" style="width: 20%;">
                        {# Check for custom_specific_rage first #}
                        {% if obs.get("custom_specific_rage") %}
                        <div>{{ obs.get("custom_specific_rage") }}</div>
                        {% else %}
                        {# Fall back to normal reference range logic #}
                        {% set template = frappe.get_doc("Observation Template", obs.get("observation_template")) %}
                        {% set ns = namespace(best_ref=None, best_priority=999) %}
                        {% for ref in template.observation_reference_range %}
                        {% if not (ref.normal_from == 0 and ref.normal_to == 0) %}
                        {% set applies_to = ref.applies_to | default("All") %}
                        {% set ref_age_type = ref.age | default("All") %}
                        {% set age_from = ref.age_from | int(0) %}
                        {% set age_to = ref.age_to | int(0) %}
                        {% set gender_match = (applies_to == "All" or applies_to.lower() == sex.lower()) %}
                        {% if ref_age_type == "All" %}
                        {% set age_match = true %}
                        {% elif ref_age_type == "Range" %}
                        {% set age_match = (years >= age_from and years <= age_to) %} {% else %} {% set age_match=false
                            %} {% endif %} {% if gender_match and age_match %} {% set priority=5 %} {# Default low
                            priority #} {% if applies_to !="All" and ref_age_type=="Range" %} {% set priority=1 %} {%
                            elif applies_to=="All" and ref_age_type=="Range" %} {% set priority=2 %} {% elif applies_to
                            !="All" and ref_age_type=="All" %} {% set priority=3 %} {% elif applies_to=="All" and
                            ref_age_type=="All" %} {% set priority=4 %} {% endif %} {% if ns.best_ref is none or
                            priority < ns.best_priority %} {% set ns.best_ref=ref %} {% set ns.best_priority=priority %}
                            {% endif %} {% endif %} {% endif %} {% endfor %} {% if ns.best_ref %} <div>{{
                            ns.best_ref.normal_from }} - {{ ns.best_ref.normal_to }}
                    </div>
                    {% else %}
                    <div class="no-reference">-</div>
                    {% endif %}
                    {% endif %}
                </div>
                <div class="obs-field" style="width: 16%;">
                    <div class="obs-first-line">
                        {{ obs.get("method") or "" }}
                    </div>
                </div>
            </div>

            {% if obs.get("result_text") and obs.get("result_text")|length > 24 %}
            <div class="note">{{ obs.get("result_text") }}</div>
            {% endif %}
            {% if obs.get("result_interpretation") %}
            <div class="note">{{ obs.get("result_interpretation") }}</div>
            {% endif %}
            {% if obs.get("note") %}
            <div class="note">{{ obs.get("note") }}</div>
            {% endif %}
            {% if obs.get("description") %}
            <div class="note">{{ obs.get("description") }}</div>
            {% endif %}
        </div>

    </div>

    {% endif %}
    {% endif %}
    {% endif %}

    {# Handle observations with components #}
    {% if data.get("has_component") and data[data.get("observation")] and data["has_result"] %}
    <div class="observation-section">
        <div class="group-title">
            <b>{{ data.get("display_name") }}</b>
        </div>
        <div class="grouped-obs">
            {% for comps in data[data.get("observation")] %}
            {% if comps is mapping %}
            {% set observation_object = comps.get("observation") %}
            {% if observation_object is mapping %}
            {% set observation_name = observation_object.get("preferred_display_name") or
            observation_object.get("observation_template") %}

            {% if observation_object.get("status") != 'Cancelled' and (observation_object.get("result_data") or
            observation_object.get("result_text") or observation_object.get("result_select") not in [None, "", "Null"])
            %}
            <div class="observation single-obs">
                <div class="observation-details">
                    <div class="obs-field" style="width: 12%;">
                        <div class="obs-first-line">
                            {{ observation_object.get("sample") or frappe.db.get_value("Observation Template",
                            observation_object.get("observation_template"), "sample") or "" }}
                        </div>
                    </div>

                    <div class="obs-field" style="width: 28%;">
                        <div class="obs-first-line">
                            {{ observation_name }}
                        </div>
                    </div>


                    <div class="obs-field" style="width: 14%;">
                        <div class="obs-first-line text-center">
                            {% if observation_object.get("result_data") or observation_object.get("result_select") %}
                            {{observation_object.get("result_data") or observation_object.get("result_select")}}
                            {% elif observation_object.get("result_text") %}
                            {% if '</div>' in observation_object.get("result_text") and
                        observation_object.get("result_text")|length <= 60 %} {{observation_object.get("result_text")}}
                            {% elif observation_object.get("result_text")|length <=24 %}
                            {{observation_object.get("result_text")}} {% endif %} {% endif %} </div>
                    </div>

                    <div class="obs-field obs-first-line" style="width: 10%;">
                        {% if observation_object.get("permitted_unit") %}
                        {{ observation_object.get("permitted_unit") }}
                        {% else %}
                        {% set obs_template = observation_object.get("observation_template") %}
                        {% if obs_template %}
                        {% set unit_from_template = frappe.db.get_value("Observation Template", obs_template, "uom") %}
                        {{ unit_from_template or "-" }}
                        {% else %}
                        -
                        {% endif %}
                        {% endif %}
                    </div>

                    <div class="obs-field ref-range" style="width: 20%;">
                        {# Check for custom_reference_rane first for component observations #}
                        {% if observation_object.get("custom_specific_rage") %}
                        <div>{{ observation_object.get("custom_specific_rage") }}</div>
                        {% else %}
                        {# Fall back to normal reference range logic for components #}
                        {% set current_sub_observation = observation_object %}
                        {% if current_sub_observation is mapping and current_sub_observation.get("observation_template")
                        %}
                        {% set template = frappe.get_doc("Observation Template",
                        current_sub_observation.get("observation_template")) %}

                        {% set ns = namespace(best_ref=None, best_priority=999) %}

                        {% for ref in template.observation_reference_range %}
                        {% if not (ref.normal_from == 0 and ref.normal_to == 0) %}

                        {% set applies_to = ref.applies_to | default("All") %}
                        {% set ref_age_type = ref.age | default("All") %}
                        {% set age_from = ref.age_from | int(0) %}
                        {% set age_to = ref.age_to | int(0) %}

                        {% set gender_match = (applies_to == "All" or applies_to.lower() == sex.lower()) %}

                        {% if ref_age_type == "All" %}
                        {% set age_match = true %}
                        {% elif ref_age_type == "Range" %}
                        {% set age_match = (years >= age_from and years <= age_to) %} {% else %} {% set age_match=false
                            %} {% endif %} {% if gender_match and age_match %} {% set priority=5 %} {# Default low
                            priority #} {% if applies_to !="All" and ref_age_type=="Range" %} {% set priority=1 %} {%
                            elif applies_to=="All" and ref_age_type=="Range" %} {% set priority=2 %} {% elif applies_to
                            !="All" and ref_age_type=="All" %} {% set priority=3 %} {% elif applies_to=="All" and
                            ref_age_type=="All" %} {% set priority=4 %} {% endif %} {% if ns.best_ref is none or
                            priority < ns.best_priority %} {% set ns.best_ref=ref %} {% set ns.best_priority=priority %}
                            {% endif %} {% endif %} {% endif %} {% endfor %} {% if ns.best_ref %} <div>
                            {{ ns.best_ref.normal_from }} - {{ ns.best_ref.normal_to }}
                    </div>
                    {% else %}
                    <div class="no-reference">
                        -
                    </div>
                    {% endif %}

                    {% else %}
                    <div class="no-reference">No reference found</div>
                    {% endif %}
                    {% endif %}


                </div>
                <div class="obs-field" style="width: 16%;">
                    {% if observation_object.get("method") %}
                    <div class="obs-first-line">
                        {{ observation_object.get("method") }}
                    </div>
                    {% endif %}
                </div>
            </div>

            {# Notes for component observation #}
            {% if observation_object.get("result_text") %}
            <div class="note">
                {{observation_object.get("result_text")}}
            </div>
            {% endif %}

            {% if observation_object.get("result_interpretation") %}
            <div class="note">
                {{observation_object.get("result_interpretation")}}
            </div>
            {% endif %}

            {% if observation_object.get("note") %}
            <div class="note">
                {{observation_object.get("note")}}
            </div>
            {% endif %}

            {% if observation_object.get("description") and not data.get("description") %}
            <div class="note">
                {{observation_object.get("description")}}
            </div>
            {% endif %}
        </div>
        {% endif %}
        {% endif %}
        {% endif %}
        {% endfor %} {# End of comps loop #}
        {% if data.get("description") %}
        <div class="note">
            {{data.get("description") or ""}}
        </div>
        {% endif %}
    </div>

    </div>
    {% endif %} {# End of has_component check #}
    {% endfor %} {# End of data in full_data_for_display[0] loop #}
    </div>
    {% endif %} {# End of doc.get("docname") and full_data_for_display check #}

    {% if doc.get("docname") and full_data_for_display %}
    {% for data in full_data_for_display[0] %}
    {% if data.get("has_component") %}
    {% set parent_observation_template = frappe.db.get_value("Observation", data.get("observation"),
    "observation_template") %}
    {% set template_doc = frappe.get_doc("Observation Template", parent_observation_template) if
    parent_observation_template else none %}

    {% if template_doc and template_doc.get("custom_component_description") %}
    <div class="component-description-section content-section">
        <h4>{{ data.get("display_name") }} - Additional Notes:</h4>
        <p>{{ template_doc.get("custom_component_description") }}</p>
    </div>
    {% endif %}
    {% endif %}
    {% endfor %}
    {% endif %}

    <div class="custom-note-section">
        {{ doc.custom_note or "" }}
    </div>

    <div class="main-footer">
        <div class="signature-section">
            <div class="signature-box">
                <div class="signature-line"></div>
                <div class="signature-label">Laboratory Technician</div>
                <div class="signature-sublabel">Name & Signature</div>
            </div>

            <div class="signature-box">
                <div class="signature-line"></div>
                <div class="signature-label">Pathologist</div>
                <div class="signature-sublabel">Name & Signature</div>
            </div>

            <div class="signature-box">
                <div class="signature-line"></div>
                <div class="signature-label">Medical Director</div>
                <div class="signature-sublabel">Name & Signature</div>
            </div>
        </div>

        {% if full_data_for_display and full_data_for_display[0] and full_data_for_display[0]|length > 0 %}
        <div class="end-of-report">
            <b>End of Report</b>
        </div>
        {% endif %}
    </div>

    </div>



</body>

</html>